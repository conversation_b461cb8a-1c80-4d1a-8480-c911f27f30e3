package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/service"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env    = flag.String("env", "prod", "运行环境: prod")
	uid    = flag.String("uid", "", "用户ID")
	roomID = flag.String("room_id", "", "房间ID")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 验证必要参数
	if *uid == "" || *roomID == "" {
		fmt.Printf("缺少必要的参数: uid=%s, room_id=%s\n", *uid, *roomID)
		os.Exit(1)
	}

	// 加载配置
	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&logger.Config{
		Level:    cfg.Log.Level,
		Format:   cfg.Log.Format,
		Output:   cfg.Log.Output,
		FilePath: cfg.Log.FilePath,
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 记录启动信息
	logger.Info("doll服务启动",
		zap.String("name", cfg.Server.Name),
		zap.String("version", Version),
		zap.String("build_time", BuildTime),
		zap.String("git_commit", GitCommit),
		zap.String("env", *env),
		zap.String("uid", *uid),
		zap.String("room_id", *roomID),
	)

	// 运行doll服务
	if err := runDollService(cfg, *uid, *roomID); err != nil {
		logger.Error("doll服务运行失败", zap.Error(err))
		os.Exit(1)
	}
}

// runDollService 运行doll服务
func runDollService(cfg *config.Config, uid, roomID string) error {
	// 处理信号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个done通道用于优雅关闭
	done := make(chan struct{})
	defer close(done)

	// 信号处理
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("doll服务接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	logger.Info("开始运行doll服务", zap.String("uid", uid), zap.String("room_id", roomID))

	// 创建doll服务管理器
	dollService, err := service.NewDollService(cfg, uid, roomID)
	if err != nil {
		logger.Error("创建doll服务失败", zap.Error(err))
		return err
	}

	// 启动doll服务
	go func() {
		if err := dollService.Start(ctx); err != nil {
			logger.Error("doll服务运行失败", zap.Error(err))
		}
		cancel() // 发生错误时取消上下文
	}()

	// 等待退出信号
	<-ctx.Done()

	// 优雅关闭
	logger.Info("开始关闭doll服务资源...")

	// 设置关闭超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// 创建一个通道用于等待清理完成
	cleanup := make(chan struct{})
	go func() {
		// 关闭doll服务
		dollService.Stop()
		close(cleanup)
	}()

	// 等待清理完成或超时
	select {
	case <-cleanup:
		logger.Info("doll服务资源已清理完成")
	case <-shutdownCtx.Done():
		logger.Warn("doll服务资源清理超时")
	}

	logger.Info("doll服务运行结束")
	return nil
}
