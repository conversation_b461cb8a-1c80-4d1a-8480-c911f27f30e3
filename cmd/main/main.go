package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env = flag.String("env", "prod", "运行环境: prod")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"

	// 子进程引用
	childProcesses = make(map[string]*os.Process)
	childMutex     = &sync.Mutex{}
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&logger.Config{
		Level:    cfg.Log.Level,
		Format:   cfg.Log.Format,
		Output:   cfg.Log.Output,
		FilePath: cfg.Log.FilePath,
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 记录启动信息
	logger.Info("主进程启动",
		zap.String("name", cfg.Server.Name),
		zap.String("version", Version),
		zap.String("build_time", BuildTime),
		zap.String("git_commit", GitCommit),
		zap.String("env", *env),
	)

	// 运行主进程
	runMainProcess(cfg)
}

// runMainProcess 运行主进程
func runMainProcess(cfg *config.Config) {
	// 创建HTTP服务器，传入启动子进程的回调函数
	httpServer := server.NewHTTPServer(cfg, startDollProcess)

	// 处理信号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("主进程接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	// 启动HTTP服务器
	go func() {
		logger.Info("启动HTTP服务器",
			zap.String("host", cfg.HTTP.Host),
			zap.Int("port", cfg.HTTP.Port),
		)
		if err := httpServer.Start(); err != nil {
			logger.Error("HTTP服务器退出", zap.Error(err))
			cancel()
		}
	}()

	// 关闭所有子进程
	defer closeAllChildProcesses()

	// 等待退出信号
	<-ctx.Done()

	// 优雅关闭
	if err := httpServer.Stop(); err != nil {
		logger.Error("HTTP服务器关闭失败", zap.Error(err))
	}
	logger.Info("HTTP服务器已关闭")
}

// startDollProcess 启动doll服务子进程的回调函数
func startDollProcess(uid, roomID string) error {
	// 生成唯一的子进程ID
	processKey := utils.GetProcessKey(uid)

	// 检查是否已经有相同ID的子进程
	childMutex.Lock()
	if _, exists := childProcesses[processKey]; exists {
		logger.Info("子进程已存在，关闭",
			zap.String("processKey", processKey),
			zap.String("uid", uid))
		// 如果子进程已存在，关闭子进程
		err := utils.CloseProcess(childProcesses[processKey].Pid, 5*time.Second)
		if err != nil {
			logger.Error("关闭子进程Error", zap.Error(err))
		}
		delete(childProcesses, processKey)
	}
	childMutex.Unlock()

	// 获取doll进程可执行文件路径
	dollPath, err := getDollExecutablePath()
	if err != nil {
		logger.Error("获取doll可执行文件路径失败", zap.Error(err))
		return err
	}

	// 构建子进程命令
	cmd := exec.Command(
		dollPath,
		"--env", *env,
		"--uid", uid,
		"--room_id", roomID,
	)

	// 设置子进程输出
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 启动子进程
	logger.Info("正在启动doll服务子进程...",
		zap.String("path", dollPath),
		zap.String("processKey", processKey),
		zap.String("uid", uid),
		zap.String("room_id", roomID),
	)
	if err := cmd.Start(); err != nil {
		logger.Error("启动doll服务子进程失败", zap.Error(err))
		return err
	}

	logger.Info("doll服务子进程已启动",
		zap.Int("pid", cmd.Process.Pid),
		zap.String("processKey", processKey),
	)

	// 保存子进程信息
	childMutex.Lock()
	childProcesses[processKey] = cmd.Process
	childMutex.Unlock()

	// 不等待子进程结束，让它在后台运行
	go func() {
		if err := cmd.Wait(); err != nil {
			logger.Error("doll服务子进程异常退出",
				zap.Error(err),
				zap.String("processKey", processKey),
			)
		}

		// 子进程结束后从映射中移除
		childMutex.Lock()
		delete(childProcesses, processKey)
		childMutex.Unlock()
	}()

	return nil
}

// getDollExecutablePath 获取doll进程可执行文件路径
func getDollExecutablePath() (string, error) {
	// 首先尝试在同一目录下查找doll可执行文件
	execPath, err := os.Executable()
	if err != nil {
		return "", err
	}

	// 假设doll可执行文件与main在同一目录
	dollPath := execPath + "-doll"
	if _, err := os.Stat(dollPath); err == nil {
		return dollPath, nil
	}

	// 如果找不到，尝试使用相对路径
	dollPath = "./aigc-server-doll"
	if _, err := os.Stat(dollPath); err == nil {
		return dollPath, nil
	}

	// 最后尝试在PATH中查找
	dollPath, err = exec.LookPath("aigc-server-doll")
	if err != nil {
		return "", fmt.Errorf("找不到doll可执行文件")
	}

	return dollPath, nil
}

// closeAllChildProcesses 关闭所有子进程
func closeAllChildProcesses() {
	logger.Info("正在关闭所有子进程...")

	childMutex.Lock()
	processes := make(map[string]*os.Process)
	for id, process := range childProcesses {
		processes[id] = process
	}
	childMutex.Unlock()

	// 使用WaitGroup来等待所有子进程关闭
	var wg sync.WaitGroup
	for id, process := range processes {
		wg.Add(1)
		go func(id string, process *os.Process) {
			defer wg.Done()

			logger.Info("正在关闭子进程",
				zap.String("id", id),
				zap.Int("pid", process.Pid),
			)

			if err := utils.CloseProcess(process.Pid, 5*time.Second); err != nil {
				logger.Error("关闭子进程失败",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
					zap.Error(err),
				)
			} else {
				logger.Info("子进程已关闭",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
				)
			}

			// 从映射中移除子进程
			childMutex.Lock()
			delete(childProcesses, id)
			childMutex.Unlock()
		}(id, process)
	}

	// 等待所有子进程处理完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 设置总体超时时间
	select {
	case <-done:
		logger.Info("所有子进程已关闭")
	case <-time.After(10 * time.Second):
		logger.Error("部分子进程可能未正常关闭")
	}
}
