module aigc_server

go 1.24.2

require (
	bytedance/bytertc v0.0.0-00010101000000-000000000000
	github.com/hajimehoshi/go-mp3 v0.3.4
	github.com/mewkiz/flac v1.0.12
	github.com/spf13/viper v1.20.1
	github.com/youpy/go-wav v0.3.2
	go.uber.org/zap v1.27.0
	golang.org/x/net v0.35.0
	google.golang.org/grpc v1.72.0
	google.golang.org/protobuf v1.36.5
)

require (
	github.com/fsnotify/fsnotify v1.8.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/ianlancetaylor/cgosymbolizer v0.0.0-20240805235558-330cf6901bf7 // indirect
	github.com/icza/bitio v1.1.0 // indirect
	github.com/mewkiz/pkg v0.0.0-20230226050401-4010bf0fec14 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/sagikazarmark/locafero v0.7.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.12.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/youpy/go-riff v0.1.0 // indirect
	github.com/zaf/g711 v0.0.0-20190814101024-76a4a538f52b // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250218202821-56aae31c358a // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace aigc_server/proto => ./proto/protos

replace bytedance/bytertc => ./sdk/VolcEngineRTC_Linux_3.60.1.29857238_x86_64_Release/bytedance/bytertc
