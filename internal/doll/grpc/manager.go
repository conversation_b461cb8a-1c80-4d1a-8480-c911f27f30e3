package grpc

import (
	"context"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

// Manager gRPC连接管理器
type Manager struct {
	cfg *config.Config
	uid string

	// 连接
	asrConn *grpc.ClientConn
	llmConn *grpc.ClientConn
	ttsConn *grpc.ClientConn

	// 客户端
	asrClient proto.ASRClient
	llmClient proto.LLMChatClient
	ttsClient proto.TTSClient

	// 流
	asrStream proto.ASR_ASRClient
	llmStream proto.LLMChat_ChatClient
	ttsStream proto.TTS_TTSClient

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 互斥锁
	asrMutex sync.Mutex
	llmMutex sync.Mutex
	ttsMutex sync.Mutex

	// 状态
	closed bool
	mu     sync.RWMutex

	// 事件处理器
	handlers []EventHandler
}

// EventHandler gRPC事件处理器接口
type EventHandler interface {
	OnASRResponse(ctx context.Context, response *proto.ASRResponse) error
	OnLLMResponse(ctx context.Context, response *proto.LLMResponse) error
	OnTTSResponse(ctx context.Context, response *proto.TTSResponse) error
}

// NewManager 创建gRPC管理器
func NewManager(cfg *config.Config, uid string) (*Manager, error) {
	md := metadata.New(map[string]string{
		"uid": uid,
	})
	ctx := metadata.NewOutgoingContext(context.Background(), md)
	ctx, cancel := context.WithCancel(ctx)

	manager := &Manager{
		cfg:      cfg,
		uid:      uid,
		ctx:      ctx,
		cancel:   cancel,
		handlers: make([]EventHandler, 0),
	}

	// 初始化连接
	if err := manager.initConnections(); err != nil {
		cancel()
		return nil, err
	}

	return manager, nil
}

// AddEventHandler 添加事件处理器
func (m *Manager) AddEventHandler(handler EventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.handlers = append(m.handlers, handler)
}

// initConnections 初始化所有gRPC连接
func (m *Manager) initConnections() error {
	logger.Info("初始化gRPC连接", zap.String("uid", m.uid))

	// 初始化ASR连接
	if m.cfg.GRPC.AsrHost != "" {
		if err := m.initASRConnection(); err != nil {
			logger.Error("初始化ASR连接失败", zap.Error(err))
			go m.reconnectASR()
		}
	}

	// 初始化LLM连接
	if m.cfg.GRPC.LlmHost != "" {
		if err := m.initLLMConnection(); err != nil {
			logger.Error("初始化LLM连接失败", zap.Error(err))
			go m.reconnectLLM()
		}
	}

	// 初始化TTS连接
	if m.cfg.GRPC.TtsHost != "" {
		if err := m.initTTSConnection(); err != nil {
			logger.Error("初始化TTS连接失败", zap.Error(err))
			go m.reconnectTTS()
		}
	}

	logger.Info("gRPC连接初始化完成", zap.String("uid", m.uid))
	return nil
}

// initASRConnection 初始化ASR连接
func (m *Manager) initASRConnection() error {
	m.asrMutex.Lock()
	defer m.asrMutex.Unlock()

	var err error
	m.asrConn, err = grpc.Dial(m.cfg.GRPC.AsrHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return err
	}

	m.asrClient = proto.NewASRClient(m.asrConn)
	m.asrStream, err = m.asrClient.ASR(m.ctx)
	if err != nil {
		return err
	}

	logger.Info("ASR连接已初始化", zap.String("endpoint", m.cfg.GRPC.AsrHost))

	// 启动接收ASR响应的goroutine
	go m.receiveASRResponses()
	return nil
}

// initLLMConnection 初始化LLM连接
func (m *Manager) initLLMConnection() error {
	m.llmMutex.Lock()
	defer m.llmMutex.Unlock()

	var err error
	m.llmConn, err = grpc.Dial(m.cfg.GRPC.LlmHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return err
	}

	m.llmClient = proto.NewLLMChatClient(m.llmConn)
	m.llmStream, err = m.llmClient.Chat(m.ctx)
	if err != nil {
		return err
	}

	logger.Info("LLM连接已初始化", zap.String("endpoint", m.cfg.GRPC.LlmHost))

	// 启动接收LLM响应的goroutine
	go m.receiveLLMResponses()
	return nil
}

// initTTSConnection 初始化TTS连接
func (m *Manager) initTTSConnection() error {
	m.ttsMutex.Lock()
	defer m.ttsMutex.Unlock()

	var err error
	m.ttsConn, err = grpc.Dial(m.cfg.GRPC.TtsHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return err
	}

	m.ttsClient = proto.NewTTSClient(m.ttsConn)
	m.ttsStream, err = m.ttsClient.TTS(m.ctx)
	if err != nil {
		return err
	}

	logger.Info("TTS连接已初始化", zap.String("endpoint", m.cfg.GRPC.TtsHost))

	// 启动接收TTS响应的goroutine
	go m.receiveTTSResponses()
	return nil
}

// Start 启动gRPC管理器
func (m *Manager) Start(ctx context.Context) error {
	logger.Info("gRPC管理器启动", zap.String("uid", m.uid))

	// 等待上下文取消
	<-ctx.Done()

	logger.Info("gRPC管理器停止", zap.String("uid", m.uid))
	return nil
}

// Close 关闭gRPC管理器
func (m *Manager) Close() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return
	}
	m.closed = true

	logger.Info("关闭gRPC管理器", zap.String("uid", m.uid))

	// 取消上下文
	m.cancel()

	// 关闭连接
	if m.asrConn != nil {
		m.asrConn.Close()
	}
	if m.llmConn != nil {
		m.llmConn.Close()
	}
	if m.ttsConn != nil {
		m.ttsConn.Close()
	}

	// 等待所有goroutine完成
	m.wg.Wait()

	logger.Info("gRPC管理器已关闭", zap.String("uid", m.uid))
}

// SendASRAudio 发送音频数据到ASR服务
func (m *Manager) SendASRAudio(audio []byte) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.closed || m.asrStream == nil {
		return nil
	}

	m.asrMutex.Lock()
	defer m.asrMutex.Unlock()

	request := &proto.ASRRequest{
		Audio: audio,
	}

	return m.asrStream.Send(request)
}

// SendLLMMessage 发送消息到LLM服务
func (m *Manager) SendLLMMessage(content string, isFinal bool) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.closed || m.llmStream == nil {
		return nil
	}

	m.llmMutex.Lock()
	defer m.llmMutex.Unlock()

	request := &proto.LLMRequest{
		Content: content,
		IsFinal: isFinal,
	}

	return m.llmStream.Send(request)
}

// SendTTSText 发送文本到TTS服务
func (m *Manager) SendTTSText(text string, index int32, segmentIndex int32, isFinal bool) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.closed || m.ttsStream == nil {
		return nil
	}

	m.ttsMutex.Lock()
	defer m.ttsMutex.Unlock()

	request := &proto.TTSRequest{
		Text:         text,
		Index:        index,
		SegmentIndex: segmentIndex,
		IsFinal:      isFinal,
	}

	return m.ttsStream.Send(request)
}

// receiveASRResponses 接收ASR响应
func (m *Manager) receiveASRResponses() {
	m.wg.Add(1)
	defer m.wg.Done()

	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if m.asrStream == nil {
				time.Sleep(time.Second)
				continue
			}

			response, err := m.asrStream.Recv()
			if err != nil {
				logger.Error("接收ASR响应失败", zap.Error(err))
				go m.reconnectASR()
				return
			}

			// 通知所有处理器
			m.mu.RLock()
			for _, handler := range m.handlers {
				if err := handler.OnASRResponse(m.ctx, response); err != nil {
					logger.Error("处理ASR响应失败", zap.Error(err))
				}
			}
			m.mu.RUnlock()
		}
	}
}

// receiveLLMResponses 接收LLM响应
func (m *Manager) receiveLLMResponses() {
	m.wg.Add(1)
	defer m.wg.Done()

	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if m.llmStream == nil {
				time.Sleep(time.Second)
				continue
			}

			response, err := m.llmStream.Recv()
			if err != nil {
				logger.Error("接收LLM响应失败", zap.Error(err))
				go m.reconnectLLM()
				return
			}

			// 通知所有处理器
			m.mu.RLock()
			for _, handler := range m.handlers {
				if err := handler.OnLLMResponse(m.ctx, response); err != nil {
					logger.Error("处理LLM响应失败", zap.Error(err))
				}
			}
			m.mu.RUnlock()
		}
	}
}

// receiveTTSResponses 接收TTS响应
func (m *Manager) receiveTTSResponses() {
	m.wg.Add(1)
	defer m.wg.Done()

	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if m.ttsStream == nil {
				time.Sleep(time.Second)
				continue
			}

			response, err := m.ttsStream.Recv()
			if err != nil {
				logger.Error("接收TTS响应失败", zap.Error(err))
				go m.reconnectTTS()
				return
			}

			// 通知所有处理器
			m.mu.RLock()
			for _, handler := range m.handlers {
				if err := handler.OnTTSResponse(m.ctx, response); err != nil {
					logger.Error("处理TTS响应失败", zap.Error(err))
				}
			}
			m.mu.RUnlock()
		}
	}
}

// reconnectASR 重连ASR服务
func (m *Manager) reconnectASR() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-time.After(5 * time.Second):
			logger.Info("尝试重连ASR服务")
			if err := m.initASRConnection(); err != nil {
				logger.Error("重连ASR服务失败", zap.Error(err))
			} else {
				logger.Info("ASR服务重连成功")
				return
			}
		}
	}
}

// reconnectLLM 重连LLM服务
func (m *Manager) reconnectLLM() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-time.After(5 * time.Second):
			logger.Info("尝试重连LLM服务")
			if err := m.initLLMConnection(); err != nil {
				logger.Error("重连LLM服务失败", zap.Error(err))
			} else {
				logger.Info("LLM服务重连成功")
				return
			}
		}
	}
}

// reconnectTTS 重连TTS服务
func (m *Manager) reconnectTTS() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-time.After(5 * time.Second):
			logger.Info("尝试重连TTS服务")
			if err := m.initTTSConnection(); err != nil {
				logger.Error("重连TTS服务失败", zap.Error(err))
			} else {
				logger.Info("TTS服务重连成功")
				return
			}
		}
	}
}
