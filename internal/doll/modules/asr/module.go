package asr

import (
	"context"
	"sync"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/grpc"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"

	"go.uber.org/zap"
)

// Module ASR模块，负责语音识别相关功能
type Module struct {
	cfg         *config.Config
	uid         string
	grpcManager *grpc.Manager

	// 事件处理器
	handlers []EventHandler

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex
}

// EventHandler ASR事件处理器接口
type EventHandler interface {
	OnASRResult(ctx context.Context, text string, isFinal bool) error
}

// NewModule 创建ASR模块
func NewModule(cfg *config.Config, uid string, grpcManager *grpc.Manager) (*Module, error) {
	ctx, cancel := context.WithCancel(context.Background())

	module := &Module{
		cfg:         cfg,
		uid:         uid,
		grpcManager: grpcManager,
		ctx:         ctx,
		cancel:      cancel,
		handlers:    make([]EventHandler, 0),
	}

	// 注册为gRPC事件处理器
	grpcManager.AddEventHandler(module)

	logger.Info("ASR模块创建成功", zap.String("uid", uid))
	return module, nil
}

// AddEventHandler 添加事件处理器
func (m *Module) AddEventHandler(handler EventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.handlers = append(m.handlers, handler)
}

// Start 启动ASR模块
func (m *Module) Start(ctx context.Context) error {
	logger.Info("ASR模块启动", zap.String("uid", m.uid))

	// 等待上下文取消
	<-ctx.Done()

	logger.Info("ASR模块停止", zap.String("uid", m.uid))
	return nil
}

// Stop 停止ASR模块
func (m *Module) Stop() {
	logger.Info("停止ASR模块", zap.String("uid", m.uid))

	// 取消上下文
	m.cancel()

	// 等待所有goroutine完成
	m.wg.Wait()

	logger.Info("ASR模块已停止", zap.String("uid", m.uid))
}

// SendAudio 发送音频数据进行语音识别
func (m *Module) SendAudio(audio []byte) error {
	return m.grpcManager.SendASRAudio(audio)
}

// OnASRResponse 实现grpc.EventHandler接口，处理ASR响应
func (m *Module) OnASRResponse(ctx context.Context, response *proto.ASRResponse) error {
	logger.Debug("收到ASR响应",
		zap.String("uid", m.uid),
		zap.String("text", response.Text),
		zap.Bool("is_final", response.IsFinal),
	)

	// 通知所有处理器
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, handler := range m.handlers {
		if err := handler.OnASRResult(ctx, response.Text, response.IsFinal); err != nil {
			logger.Error("处理ASR结果失败", zap.Error(err))
		}
	}

	return nil
}

// OnLLMResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnLLMResponse(ctx context.Context, response *proto.LLMResponse) error {
	// ASR模块不处理LLM响应
	return nil
}

// OnTTSResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnTTSResponse(ctx context.Context, response *proto.TTSResponse) error {
	// ASR模块不处理TTS响应
	return nil
}
