package llm

import (
	"context"
	"sync"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/grpc"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"

	"go.uber.org/zap"
)

// Module LLM模块，负责大语言模型相关功能
type Module struct {
	cfg         *config.Config
	uid         string
	grpcManager *grpc.Manager

	// 事件处理器
	handlers []EventHandler

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex
}

// EventHandler LLM事件处理器接口
type EventHandler interface {
	OnLLMResult(ctx context.Context, response *proto.LLMResponse) error
}

// NewModule 创建LLM模块
func NewModule(cfg *config.Config, uid string, grpcManager *grpc.Manager) (*Module, error) {
	ctx, cancel := context.WithCancel(context.Background())

	module := &Module{
		cfg:         cfg,
		uid:         uid,
		grpcManager: grpcManager,
		ctx:         ctx,
		cancel:      cancel,
		handlers:    make([]EventHandler, 0),
	}

	// 注册为gRPC事件处理器
	grpcManager.AddEventHandler(module)

	logger.Info("LLM模块创建成功", zap.String("uid", uid))
	return module, nil
}

// AddEventHandler 添加事件处理器
func (m *Module) AddEventHandler(handler EventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.handlers = append(m.handlers, handler)
}

// Start 启动LLM模块
func (m *Module) Start(ctx context.Context) error {
	logger.Info("LLM模块启动", zap.String("uid", m.uid))

	// 等待上下文取消
	<-ctx.Done()

	logger.Info("LLM模块停止", zap.String("uid", m.uid))
	return nil
}

// Stop 停止LLM模块
func (m *Module) Stop() {
	logger.Info("停止LLM模块", zap.String("uid", m.uid))

	// 取消上下文
	m.cancel()

	// 等待所有goroutine完成
	m.wg.Wait()

	logger.Info("LLM模块已停止", zap.String("uid", m.uid))
}

// SendMessage 发送消息到LLM服务
func (m *Module) SendMessage(content string, isFinal bool) error {
	logger.Debug("发送LLM消息",
		zap.String("uid", m.uid),
		zap.String("content", content),
		zap.Bool("is_final", isFinal),
	)
	return m.grpcManager.SendLLMMessage(content, isFinal)
}

// OnASRResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnASRResponse(ctx context.Context, response *proto.ASRResponse) error {
	// LLM模块不处理ASR响应
	return nil
}

// OnLLMResponse 实现grpc.EventHandler接口，处理LLM响应
func (m *Module) OnLLMResponse(ctx context.Context, response *proto.LLMResponse) error {
	logger.Debug("收到LLM响应",
		zap.String("uid", m.uid),
		zap.String("content", response.Content),
		zap.Bool("is_final", response.IsFinal),
		zap.Int32("index", response.Index),
	)

	// 通知所有处理器
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, handler := range m.handlers {
		if err := handler.OnLLMResult(ctx, response); err != nil {
			logger.Error("处理LLM结果失败", zap.Error(err))
		}
	}

	return nil
}

// OnTTSResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnTTSResponse(ctx context.Context, response *proto.TTSResponse) error {
	// LLM模块不处理TTS响应
	return nil
}
