package rtc

import (
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"sync"

	"bytedance/bytertc/rtcengine"
	"go.uber.org/zap"
)

// EngineEventHandler RTC引擎事件处理器
type EngineEventHandler struct {
	rtcengine.IRTCEngineEventHandlerDefaultImp
}

// RoomEventHandler RTC房间事件处理器
type RoomEventHandler struct {
	rtcengine.IRTCRoomEventHandlerDefaultImpl
	rtc *Module
}

// AudioFrameObserver 音频帧观察器
type AudioFrameObserver struct {
	rtcengine.IAudioFrameObserverDefaultImp
	oncePrintRemoteAudioFrame sync.Once
	rtc                       *Module
}

// OnRemoteAudioFrame 处理远程音频帧
func (observer *AudioFrameObserver) OnRemoteAudioFrame(streamKey *rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	observer.oncePrintRemoteAudioFrame.Do(func() {
		logger.Info("收到远程音频帧",
			zap.String("uid", observer.rtc.uid),
			zap.String("room_id", observer.rtc.roomID),
			zap.Int("sample_rate", audioFrame.SampleRate()),
			zap.Int("channel", audioFrame.Channel()),
		)
	})

	targetSampleRate := AsrSampleRate
	targetChannel := 1

	srcFormat := utils.AudioPCMFormat{
		SampleRate: audioFrame.SampleRate(),
		Channels:   audioFrame.Channel(),
		BitDepth:   16,
	}
	dstFormat := utils.AudioPCMFormat{
		SampleRate: targetSampleRate,
		Channels:   targetChannel,
		BitDepth:   SampleBitDepth,
	}
	bytes, err := utils.ResamplePCM(audioFrame.Data(), srcFormat, dstFormat)
	if err != nil {
		logger.Error("重采样失败", zap.Error(err))
		return
	}

	// 创建音频帧并发送到通道
	audioFrameBuilder := rtcengine.AudioFrameBuilder{
		SampleRate: targetSampleRate,
		Channel:    targetChannel,
		Data:       bytes,
	}
	newFrame := rtcengine.BuildAudioFrame(audioFrameBuilder)

	select {
	case observer.rtc.audioFrameChan <- newFrame:
	default:
		// 通道满时丢弃帧
		logger.Warn("音频帧通道已满，丢弃帧")
	}
}

// OnUserJoined 用户加入房间事件
func (handler *RoomEventHandler) OnUserJoined(userInfo *rtcengine.UserInfo, elapsed int) {
	logger.Info("用户加入房间",
		zap.String("room_id", handler.rtc.roomID),
		zap.String("user_id", userInfo.UID),
		zap.Int("elapsed", elapsed),
	)
	handler.rtc.userJoinedStatus = 1
}

// OnUserLeave 用户离开房间事件
func (handler *RoomEventHandler) OnUserLeave(uid string, reason int) {
	logger.Info("用户离开房间",
		zap.String("room_id", handler.rtc.roomID),
		zap.String("user_id", uid),
		zap.Int("reason", reason),
	)
	handler.rtc.userJoinedStatus = 2
}

// OnRoomStateChanged 房间状态变化事件
func (handler *RoomEventHandler) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {
	logger.Info("房间状态变化",
		zap.String("room_id", roomId),
		zap.String("uid", uid),
		zap.Int("state", state),
		zap.String("extra_info", extraInfo),
	)
}
