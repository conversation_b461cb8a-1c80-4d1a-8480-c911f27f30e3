package rtc

import (
	"aigc_server/internal/config"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"
	"context"
	"fmt"

	"go.uber.org/zap"
)

type LLMMsgSaveRequest struct {
	DollId string `json:"dollId"`
	Query  string `json:"query"`
	Answer string `json:"answer"`
}

type MessageTransmitter struct {
	ctx          context.Context
	dollId       string
	httpRequest  *service.HttpRequest
	msgChan      chan *LLMMsgSaveRequest
	queryingMsg  *LLMMsgSaveRequest
	answeringMsg *LLMMsgSaveRequest
}

func NewMessageTransmitter(ctx context.Context, cfg *config.Config, dollId string) *MessageTransmitter {
	transmitter := &MessageTransmitter{
		ctx: ctx,
		httpRequest: service.NewHttpRequest(
			"POST",
			fmt.Sprintf("%s/doll/msg/save", cfg.HTTPClient.BaseURL),
			nil,
			map[string]string{"Authorization": "b837o#G@j@8GbFH@"},
			cfg.HTTPClient.TimeoutSeconds,
		),
		dollId: dollId,
	}
	transmitter.Start()
	return transmitter
}

func (t *MessageTransmitter) Start() {
	go t.loopSaveMsg()
}

func (t *MessageTransmitter) loopSaveMsg() {
	logger.Info("LLM消息传输器启动")
	t.msgChan = make(chan *LLMMsgSaveRequest, 20)
	defer close(t.msgChan)
	for {
		select {
		case <-t.ctx.Done():
			logger.Info("LLM消息传输器停止")
			return
		case msg := <-t.msgChan:
			t.saveMsg(msg)
		}
	}
}

func (t *MessageTransmitter) saveMsg(msg *LLMMsgSaveRequest) {
	logger.Debug("保存LLM消息", zap.Any("msg", msg))
	t.httpRequest.Body = msg
	_, err := t.httpRequest.DoRequest(t.ctx)
	if err != nil {
		logger.Error("保存LLM消息失败", zap.Error(err), zap.Any("msg", msg))
	}
}

func (t *MessageTransmitter) OnLlmRequest(asrRes *proto.ASRResponse) error {
	if t.queryingMsg == nil {
		t.queryingMsg = &LLMMsgSaveRequest{
			DollId: t.dollId,
			Query:  "",
			Answer: "",
		}
		logger.Info("开始保存新消息", zap.String("dollId", t.dollId))
	}
	t.queryingMsg.Query += asrRes.Text
	if asrRes.IsFinal {
		logger.Info("查询消息完成", zap.Any("msg", t.queryingMsg))
		t.answeringMsg = t.queryingMsg
		t.queryingMsg = nil
	}
	return nil
}

func (t *MessageTransmitter) OnLlmResponse(response *proto.LLMResponse) error {
	if t.answeringMsg == nil {
		logger.Warn("回答消息为空，跳过LLM响应", zap.Any("LLMResponse", response))
		return nil
	}
	if response.Content != "" && response.Content != "[NoResponse]" {
		t.answeringMsg.Answer += response.Content
	}
	if response.IsFinal {
		newMsg := t.answeringMsg
		t.answeringMsg = nil
		logger.Info("回答消息完成", zap.Any("msg", newMsg), zap.Any("LLM", response))
		t.msgChan <- newMsg
	}
	return nil
}
