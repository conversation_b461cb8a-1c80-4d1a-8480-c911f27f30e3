package rtc

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/modules/asr"
	"aigc_server/internal/doll/modules/llm"
	"aigc_server/internal/doll/modules/tts"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
)

// Module RTC模块，负责实时音视频通信相关功能
type Module struct {
	cfg    *config.Config
	uid    string
	roomID string

	// 依赖的其他模块
	asrModule *asr.Module
	llmModule *llm.Module
	ttsModule *tts.Module

	// RTC引擎相关
	engine           rtcengine.IRTCEngine
	room             rtcengine.IRTCRoom
	userJoinedStatus int // 0: 未加入房间, 1: 加入房间, 2: 离开房间
	serverUid        string

	// 音频处理
	audioFrameChan        chan rtcengine.IAudioFrame
	voiceFrameSourceQueue []*VoiceFrameSource
	mutexFrameSourceQueue sync.Mutex
	audioInterruptIndex   int32
	interruptPauseChan    chan struct{}

	// 网络处理器
	netHandler *NetHandler

	// LLM消息传输器
	msgTransmitter *MessageTransmitter

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewModule 创建RTC模块
func NewModule(cfg *config.Config, uid, roomID string, asrModule *asr.Module, llmModule *llm.Module, ttsModule *tts.Module) (*Module, error) {
	ctx, cancel := context.WithCancel(context.Background())
	serverUid := utils.GetRTCServerUserId(uid)

	module := &Module{
		cfg:       cfg,
		uid:       uid,
		roomID:    roomID,
		serverUid: serverUid,
		asrModule: asrModule,
		llmModule: llmModule,
		ttsModule: ttsModule,
		ctx:       ctx,
		cancel:    cancel,
	}

	// 初始化音频相关
	module.audioFrameChan = make(chan rtcengine.IAudioFrame, 100)
	module.voiceFrameSourceQueue = make([]*VoiceFrameSource, 0)

	// 创建网络处理器
	module.netHandler = NewNetHandler(module)

	// 创建LLM消息传输器
	module.msgTransmitter = NewMessageTransmitter(ctx, cfg, uid)

	// 注册事件处理器
	asrModule.AddEventHandler(module)
	llmModule.AddEventHandler(module)
	ttsModule.AddEventHandler(module)

	logger.Info("RTC模块创建成功",
		zap.String("uid", uid),
		zap.String("room_id", roomID),
		zap.String("server_uid", serverUid),
	)

	return module, nil
}

// Start 启动RTC模块
func (m *Module) Start(ctx context.Context) error {
	logger.Info("RTC模块启动", zap.String("uid", m.uid), zap.String("room_id", m.roomID))

	// 启动RTC房间
	if err := m.startRoom(); err != nil {
		return fmt.Errorf("启动RTC房间失败: %w", err)
	}

	// 启动音频处理循环
	m.wg.Add(3)
	go func() {
		defer m.wg.Done()
		if err := m.loopReceiveAudioFrame(); err != nil {
			logger.Error("音频接收循环失败", zap.Error(err))
		}
	}()

	go func() {
		defer m.wg.Done()
		if err := m.loopDispatchAudioFrame(); err != nil {
			logger.Error("音频分发循环失败", zap.Error(err))
		}
	}()

	go func() {
		defer m.wg.Done()
		if err := m.roomLooping(); err != nil {
			logger.Error("房间循环失败", zap.Error(err))
		}
	}()

	// 等待上下文取消
	<-ctx.Done()

	logger.Info("RTC模块停止", zap.String("uid", m.uid))
	return nil
}

// Stop 停止RTC模块
func (m *Module) Stop() {
	logger.Info("停止RTC模块", zap.String("uid", m.uid))

	// 取消上下文
	m.cancel()

	// 停止RTC房间
	m.stopRoom()

	// 等待所有goroutine完成
	m.wg.Wait()

	logger.Info("RTC模块已停止", zap.String("uid", m.uid))
}

// startRoom 启动RTC房间
func (m *Module) startRoom() error {
	logger.Info("启动RTC房间",
		zap.String("roomID", m.roomID),
		zap.String("uid", m.uid),
		zap.String("serverUid", m.serverUid),
	)

	appID := m.cfg.Rtc.AppID
	appKey := m.cfg.Rtc.AppKey

	// 创建引擎配置
	engineParameters := make(map[string]interface{})
	engineConfig := &rtcengine.EngineConfig{
		AppID:      appID,
		Parameters: engineParameters,
	}
	engineEventHandler := &EngineEventHandler{}

	// 创建RTC引擎
	m.engine = rtcengine.CreateRTCEngine(engineConfig, engineEventHandler)
	logger.Info("RTC引擎已创建",
		zap.String("appID", appID),
		zap.Any("parameters", engineParameters),
	)

	// 创建房间
	m.room = m.engine.CreateRTCRoom(m.roomID)
	roomEventHandler := &RoomEventHandler{rtc: m}
	if ok := m.room.SetRTCRoomEventHandler(roomEventHandler); ok != 0 {
		return fmt.Errorf("设置房间事件处理器失败")
	}

	// 生成房间Token
	roomToken, _, err := utils.GenerateRoomToken(appID, appKey, m.roomID, m.serverUid)
	if err != nil {
		return fmt.Errorf("生成房间Token失败: %w", err)
	}

	// 加入房间
	userInfo := &rtcengine.UserInfo{
		UID:      m.serverUid,
		MetaData: "",
	}
	roomConfig := &rtcengine.RTCRoomConfig{}

	if ok := m.room.JoinRoom(roomToken, userInfo, roomConfig); ok != 0 {
		return fmt.Errorf("加入房间失败")
	}

	// 设置音频帧观察器
	audioFrameObserver := &AudioFrameObserver{rtc: m}
	if ok := m.engine.RegisterAudioFrameObserver(audioFrameObserver); ok != 0 {
		return fmt.Errorf("注册音频帧观察器失败")
	}

	// 启用外部音频源
	if ok := m.engine.SetAudioSourceType(1); ok != 0 { // 1 表示外部音频源
		return fmt.Errorf("设置外部音频源失败")
	}

	logger.Info("RTC房间启动成功")
	return nil
}

// stopRoom 停止RTC房间
func (m *Module) stopRoom() {
	logger.Info("停止RTC房间")

	if m.room != nil {
		m.room.LeaveRoom()
		m.room.Destroy()
		m.room = nil
	}

	if m.engine != nil {
		// RTC引擎会自动清理资源
		m.engine = nil
	}

	// 关闭通道
	if m.audioFrameChan != nil {
		close(m.audioFrameChan)
		m.audioFrameChan = nil
	}

	logger.Info("RTC房间已停止")
}

// roomLooping RTC房间主循环
func (m *Module) roomLooping() error {
	logger.Info("RTC房间主循环启动")

	// 等待上下文取消
	<-m.ctx.Done()

	logger.Info("RTC房间主循环结束")
	return nil
}

// OnASRResult 实现asr.EventHandler接口，处理ASR结果
func (m *Module) OnASRResult(ctx context.Context, text string, isFinal bool) error {
	logger.Debug("RTC模块收到ASR结果",
		zap.String("uid", m.uid),
		zap.String("text", text),
		zap.Bool("is_final", isFinal),
	)

	// 转发给LLM模块
	return m.llmModule.SendMessage(text, isFinal)
}

// OnLLMResult 实现llm.EventHandler接口，处理LLM结果
func (m *Module) OnLLMResult(ctx context.Context, response *proto.LLMResponse) error {
	logger.Debug("RTC模块收到LLM结果",
		zap.String("uid", m.uid),
		zap.String("content", response.Content),
		zap.Bool("is_final", response.IsFinal),
	)

	// 处理工具调用
	if err := m.updateLLMToolState(ctx, response); err != nil {
		logger.Error("更新LLM工具状态失败", zap.Error(err))
	}

	// 转发给TTS模块
	if response.Content != "" && response.Content != "[NoResponse]" {
		return m.ttsModule.SendText(response.Content, response.MsgIndex, response.MsgSegmentIndex, response.IsFinal)
	}

	return nil
}

// loopReceiveAudioFrame 音频帧接收循环
func (m *Module) loopReceiveAudioFrame() error {
	for {
		select {
		case <-m.ctx.Done():
			logger.Info("音频帧接收循环结束", zap.String("uid", m.uid))
			return nil
		case frame := <-m.audioFrameChan:
			if err := m.netHandler.OnAudioFrame(&frame); err != nil {
				logger.Error("处理音频帧失败", zap.Error(err))
			}
		}
	}
}

// loopDispatchAudioFrame 音频帧分发循环
func (m *Module) loopDispatchAudioFrame() error {
	sendAudioChan := make(chan rtcengine.IAudioFrame)
	defer close(sendAudioChan)

	logger.Info("启动音频帧分发循环", zap.Int("frame_rate", FrameRate))

	go m.frameProducer(sendAudioChan)
	go m.frameConsumer(sendAudioChan)

	<-m.ctx.Done()
	return nil
}

// frameProducer 音频帧生产者
func (m *Module) frameProducer(sendAudioChan chan<- rtcengine.IAudioFrame) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("音频帧生产者异常", zap.Any("recover", r))
		}
	}()

	frameCount := 0
	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if m.interruptPauseChan != nil {
				<-m.interruptPauseChan
			}

			source := m.GetFirstVoiceFrameSource()
			var audioFrame rtcengine.IAudioFrame

			if source != nil {
				frameCount = 0
				var err error
				audioFrame, _, err = source.NextFrame()
				if err != nil {
					logger.Error("获取下一帧失败", zap.Error(err))
					continue
				}
			} else {
				if frameCount < FrameRate*2 {
					audioFrame = VoiceFrameSourceEmptyFrame()
					frameCount++
				} else {
					time.Sleep(time.Millisecond * 10)
					continue
				}
			}

			select {
			case <-m.ctx.Done():
				return
			case sendAudioChan <- audioFrame:
			}
		}
	}
}

// frameConsumer 音频帧消费者
func (m *Module) frameConsumer(sendAudioChan <-chan rtcengine.IAudioFrame) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("音频帧消费者异常", zap.Any("recover", r))
		}
	}()

	ticker := time.NewTicker(time.Millisecond * 20) // 50fps
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			select {
			case audioFrame := <-sendAudioChan:
				if m.engine != nil {
					m.engine.PushExternalAudioFrame(audioFrame)
				}
			default:
				// 没有音频帧时推送空帧
				if m.engine != nil {
					m.engine.PushExternalAudioFrame(VoiceFrameSourceEmptyFrame())
				}
			}
		}
	}
}

// GetFirstVoiceFrameSource 获取第一个语音帧源
func (m *Module) GetFirstVoiceFrameSource() *VoiceFrameSource {
	m.mutexFrameSourceQueue.Lock()
	defer m.mutexFrameSourceQueue.Unlock()

	if len(m.voiceFrameSourceQueue) == 0 {
		return nil
	}

	source := m.voiceFrameSourceQueue[0]
	if source.IsDone() {
		m.voiceFrameSourceQueue = m.voiceFrameSourceQueue[1:]
		return m.GetFirstVoiceFrameSource()
	}

	return source
}

// updateLLMToolState 更新LLM工具状态
func (m *Module) updateLLMToolState(ctx context.Context, response *proto.LLMResponse) error {
	llmToolState := LLMToolStateFromLLMResp(ctx, response, m.cfg.HTTPClient.BaseURL)
	if llmToolState.IsNoTool() {
		return nil
	}

	curSource := m.GetFirstVoiceFrameSource()
	if curSource != nil && curSource.LLMVoiceType == llmToolState.State &&
		curSource.LLMIndex == llmToolState.Index {
		return nil
	}

	logger.Info("更新LLM工具状态", zap.Any("LLMResponse", response))
	if err := llmToolState.Update(); err != nil {
		logger.Error("更新LLM工具状态失败", zap.Error(err))
		return err
	}

	if llmToolState.RtcPCM == nil {
		logger.Error("LLM工具状态音频PCM为空", zap.Any("llmToolState", llmToolState))
		return fmt.Errorf("音频PCM为空")
	}

	// 创建语音帧源
	source := NewVoiceFrameSource(llmToolState.RtcPCM, llmToolState.State, llmToolState.Index, llmToolState.SegmentIndex)
	m.mutexFrameSourceQueue.Lock()
	defer m.mutexFrameSourceQueue.Unlock()
	m.voiceFrameSourceQueue = append(m.voiceFrameSourceQueue, source)

	return nil
}

// OnTTSResult 实现tts.EventHandler接口，处理TTS结果
func (m *Module) OnTTSResult(ctx context.Context, audioDatas []byte, index int32, segmentIndex int32, isFinal bool) error {
	logger.Debug("RTC模块收到TTS结果",
		zap.String("uid", m.uid),
		zap.Int("audio_size", len(audioDatas)),
		zap.Int32("index", index),
		zap.Int32("segment_index", segmentIndex),
		zap.Bool("is_final", isFinal),
	)

	// 重采样音频到RTC推送格式
	audio8k, err := utils.ResamplePCM(audioDatas, utils.AudioPCMFormat{
		SampleRate: TtsRecordSampleRate,
		Channels:   1,
		BitDepth:   16,
	}, utils.AudioPCMFormat{
		SampleRate: RtcPushAudioSampleRate,
		Channels:   Channel,
		BitDepth:   SampleBitDepth,
	})
	if err != nil {
		logger.Error("重采样PCM失败", zap.Error(err))
		return err
	}

	// 创建语音帧源
	source := NewVoiceFrameSource(audio8k, LLMToolStateType_NO_TOOL, index, segmentIndex)
	m.mutexFrameSourceQueue.Lock()
	defer m.mutexFrameSourceQueue.Unlock()
	m.voiceFrameSourceQueue = append(m.voiceFrameSourceQueue, source)

	return nil
}
