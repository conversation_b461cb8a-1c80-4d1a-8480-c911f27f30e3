package rtc

import (
	"aigc_server/pkg/logger"
	"time"

	"bytedance/bytertc/rtcengine"
	"go.uber.org/zap"
)

type AudioFrameBuffer struct {
	data      []byte
	timestamp time.Time
}

type NetHandler struct {
	rtc           *Module
	frameBuffer   []AudioFrameBuffer
	bufferSize    int
	bufferTimeout time.Duration
	lastSendTime  time.Time
}

func NewNetHandler(rtc *Module) *NetHandler {
	return &NetHandler{
		rtc:           rtc,
		frameBuffer:   make([]AudioFrameBuffer, 0),
		bufferSize:    3200,                   // bytes threshold
		bufferTimeout: 500 * time.Millisecond, // timeout
		lastSendTime:  time.Now(),
	}
}

func (h *NetHandler) OnAudioFrame(frame *rtcengine.IAudioFrame) error {
	if frame == nil {
		return nil
	}

	data := (*frame).Data()
	if len(data) == 0 {
		return nil
	}

	// 添加到缓冲区
	h.frameBuffer = append(h.frameBuffer, AudioFrameBuffer{
		data:      data,
		timestamp: time.Now(),
	})

	// 检查是否需要发送
	totalSize := 0
	for _, buffer := range h.frameBuffer {
		totalSize += len(buffer.data)
	}

	shouldSend := false
	if totalSize >= h.bufferSize {
		shouldSend = true
	} else if time.Since(h.lastSendTime) >= h.bufferTimeout && len(h.frameBuffer) > 0 {
		shouldSend = true
	}

	if shouldSend {
		return h.sendBufferedAudio()
	}

	return nil
}

func (h *NetHandler) sendBufferedAudio() error {
	if len(h.frameBuffer) == 0 {
		return nil
	}

	// 合并所有缓冲的音频数据
	totalSize := 0
	for _, buffer := range h.frameBuffer {
		totalSize += len(buffer.data)
	}

	combinedData := make([]byte, 0, totalSize)
	for _, buffer := range h.frameBuffer {
		combinedData = append(combinedData, buffer.data...)
	}

	// 发送到ASR模块
	if err := h.rtc.asrModule.SendAudio(combinedData); err != nil {
		logger.Error("发送音频到ASR模块失败", zap.Error(err))
	}

	// 清空缓冲区
	h.frameBuffer = h.frameBuffer[:0]
	h.lastSendTime = time.Now()

	logger.Debug("发送缓冲音频",
		zap.String("uid", h.rtc.uid),
		zap.Int("size", totalSize),
	)

	return nil
}
