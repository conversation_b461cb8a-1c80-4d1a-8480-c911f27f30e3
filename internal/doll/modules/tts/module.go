package tts

import (
	"context"
	"sync"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/grpc"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"

	"go.uber.org/zap"
)

// Module TTS模块，负责文本转语音相关功能
type Module struct {
	cfg         *config.Config
	uid         string
	grpcManager *grpc.Manager

	// 事件处理器
	handlers []EventHandler

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex
}

// EventHandler TTS事件处理器接口
type EventHandler interface {
	OnTTSResult(ctx context.Context, audioDatas []byte, index int32, segmentIndex int32, isFinal bool) error
}

// NewModule 创建TTS模块
func NewModule(cfg *config.Config, uid string, grpcManager *grpc.Manager) (*Module, error) {
	ctx, cancel := context.WithCancel(context.Background())

	module := &Module{
		cfg:         cfg,
		uid:         uid,
		grpcManager: grpcManager,
		ctx:         ctx,
		cancel:      cancel,
		handlers:    make([]EventHandler, 0),
	}

	// 注册为gRPC事件处理器
	grpcManager.AddEventHandler(module)

	logger.Info("TTS模块创建成功", zap.String("uid", uid))
	return module, nil
}

// AddEventHandler 添加事件处理器
func (m *Module) AddEventHandler(handler EventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.handlers = append(m.handlers, handler)
}

// Start 启动TTS模块
func (m *Module) Start(ctx context.Context) error {
	logger.Info("TTS模块启动", zap.String("uid", m.uid))

	// 等待上下文取消
	<-ctx.Done()

	logger.Info("TTS模块停止", zap.String("uid", m.uid))
	return nil
}

// Stop 停止TTS模块
func (m *Module) Stop() {
	logger.Info("停止TTS模块", zap.String("uid", m.uid))

	// 取消上下文
	m.cancel()

	// 等待所有goroutine完成
	m.wg.Wait()

	logger.Info("TTS模块已停止", zap.String("uid", m.uid))
}

// SendText 发送文本到TTS服务
func (m *Module) SendText(text string, index int32, segmentIndex int32, isFinal bool) error {
	logger.Debug("发送TTS文本",
		zap.String("uid", m.uid),
		zap.String("text", text),
		zap.Int32("index", index),
		zap.Int32("segment_index", segmentIndex),
		zap.Bool("is_final", isFinal),
	)
	return m.grpcManager.SendTTSText(text, index, segmentIndex, isFinal)
}

// OnASRResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnASRResponse(ctx context.Context, response *proto.ASRResponse) error {
	// TTS模块不处理ASR响应
	return nil
}

// OnLLMResponse 实现grpc.EventHandler接口（空实现）
func (m *Module) OnLLMResponse(ctx context.Context, response *proto.LLMResponse) error {
	// TTS模块不处理LLM响应
	return nil
}

// OnTTSResponse 实现grpc.EventHandler接口，处理TTS响应
func (m *Module) OnTTSResponse(ctx context.Context, response *proto.TTSResponse) error {
	logger.Debug("收到TTS响应",
		zap.String("uid", m.uid),
		zap.Int("audio_size", len(response.Audio)),
		zap.Int32("index", response.Index),
		zap.Int32("segment_index", response.SegmentIndex),
		zap.Bool("is_final", response.IsFinal),
	)

	// 通知所有处理器
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, handler := range m.handlers {
		if err := handler.OnTTSResult(ctx, response.Audio, response.Index, response.SegmentIndex, response.IsFinal); err != nil {
			logger.Error("处理TTS结果失败", zap.Error(err))
		}
	}

	return nil
}
