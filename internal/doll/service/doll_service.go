package service

import (
	"context"
	"fmt"
	"sync"

	"aigc_server/internal/config"
	"aigc_server/internal/doll/grpc"
	"aigc_server/internal/doll/modules/asr"
	"aigc_server/internal/doll/modules/llm"
	"aigc_server/internal/doll/modules/rtc"
	"aigc_server/internal/doll/modules/tts"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

// DollService doll服务管理器，负责协调各个服务模块
type DollService struct {
	cfg    *config.Config
	uid    string
	roomID string

	// gRPC客户端管理器
	grpcManager *grpc.Manager

	// 服务模块
	rtcModule *rtc.Module
	asrModule *asr.Module
	llmModule *llm.Module
	ttsModule *tts.Module

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewDollService 创建doll服务
func NewDollService(cfg *config.Config, uid, roomID string) (*DollService, error) {
	ctx, cancel := context.WithCancel(context.Background())

	service := &DollService{
		cfg:    cfg,
		uid:    uid,
		roomID: roomID,
		ctx:    ctx,
		cancel: cancel,
	}

	// 初始化gRPC管理器
	grpcManager, err := grpc.NewManager(cfg, uid)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建gRPC管理器失败: %w", err)
	}
	service.grpcManager = grpcManager

	// 初始化各个模块
	if err := service.initModules(); err != nil {
		cancel()
		grpcManager.Close()
		return nil, fmt.Errorf("初始化模块失败: %w", err)
	}

	return service, nil
}

// initModules 初始化各个服务模块
func (s *DollService) initModules() error {
	var err error

	// 初始化ASR模块
	s.asrModule, err = asr.NewModule(s.cfg, s.uid, s.grpcManager)
	if err != nil {
		return fmt.Errorf("初始化ASR模块失败: %w", err)
	}

	// 初始化LLM模块
	s.llmModule, err = llm.NewModule(s.cfg, s.uid, s.grpcManager)
	if err != nil {
		return fmt.Errorf("初始化LLM模块失败: %w", err)
	}

	// 初始化TTS模块
	s.ttsModule, err = tts.NewModule(s.cfg, s.uid, s.grpcManager)
	if err != nil {
		return fmt.Errorf("初始化TTS模块失败: %w", err)
	}

	// 初始化RTC模块（依赖其他模块）
	s.rtcModule, err = rtc.NewModule(s.cfg, s.uid, s.roomID, s.asrModule, s.llmModule, s.ttsModule)
	if err != nil {
		return fmt.Errorf("初始化RTC模块失败: %w", err)
	}

	logger.Info("所有服务模块初始化完成",
		zap.String("uid", s.uid),
		zap.String("room_id", s.roomID),
	)

	return nil
}

// Start 启动doll服务
func (s *DollService) Start(ctx context.Context) error {
	logger.Info("启动doll服务", zap.String("uid", s.uid), zap.String("room_id", s.roomID))

	// 启动gRPC管理器
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.grpcManager.Start(s.ctx); err != nil {
			logger.Error("gRPC管理器运行失败", zap.Error(err))
		}
	}()

	// 启动ASR模块
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.asrModule.Start(s.ctx); err != nil {
			logger.Error("ASR模块运行失败", zap.Error(err))
		}
	}()

	// 启动LLM模块
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.llmModule.Start(s.ctx); err != nil {
			logger.Error("LLM模块运行失败", zap.Error(err))
		}
	}()

	// 启动TTS模块
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.ttsModule.Start(s.ctx); err != nil {
			logger.Error("TTS模块运行失败", zap.Error(err))
		}
	}()

	// 启动RTC模块（最后启动，因为它依赖其他模块）
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.rtcModule.Start(s.ctx); err != nil {
			logger.Error("RTC模块运行失败", zap.Error(err))
		}
	}()

	logger.Info("doll服务所有模块已启动")

	// 等待所有模块完成
	s.wg.Wait()

	return nil
}

// Stop 停止doll服务
func (s *DollService) Stop() {
	logger.Info("停止doll服务", zap.String("uid", s.uid))

	// 取消上下文，通知所有模块停止
	s.cancel()

	// 按顺序停止模块（与启动顺序相反）
	if s.rtcModule != nil {
		s.rtcModule.Stop()
	}

	if s.ttsModule != nil {
		s.ttsModule.Stop()
	}

	if s.llmModule != nil {
		s.llmModule.Stop()
	}

	if s.asrModule != nil {
		s.asrModule.Stop()
	}

	// 关闭gRPC管理器
	if s.grpcManager != nil {
		s.grpcManager.Close()
	}

	// 等待所有goroutine完成
	s.wg.Wait()

	logger.Info("doll服务已停止", zap.String("uid", s.uid))
}
