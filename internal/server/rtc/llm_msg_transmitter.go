package rtc

import (
	"aigc_server/internal/config"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"
	"fmt"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type LLMMsgSaveRequest struct {
	DollId string `json:"dollId"`
	Query  string `json:"query"`
	Answer string `json:"answer"`
}

type LLMMessageTransmitter struct {
	ctx          context.Context
	dollId       string
	httpRequest  *service.HttpRequest
	msgChan      chan *LLMMsgSaveRequest
	queryingMsg  *LLMMsgSaveRequest
	answeringMsg *LLMMsgSaveRequest
}

func NewLLMMessageTransmitter(ctx context.Context, cfg *config.Config, dollId string) *LLMMessageTransmitter {
	transmitter := &LLMMessageTransmitter{
		ctx: ctx,
		httpRequest: service.NewHttpRequest(
			"POST",
			fmt.Sprintf("%s/doll/msg/save", cfg.HTTPClient.BaseURL),
			nil,
			map[string]string{"Authorization": "b837o#G@j@8GbFH@"},
			cfg.HTTPClient.TimeoutSeconds,
		),
		dollId: dollId,
	}
	transmitter.Start()
	return transmitter
}

func (t *LLMMessageTransmitter) Start() {
	go t.loopSaveMsg()
}
func (t *LLMMessageTransmitter) loopSaveMsg() {
	logger.Info("LLMMessageTransmitter loopSaveMsg start")
	t.msgChan = make(chan *LLMMsgSaveRequest, 20)
	defer close(t.msgChan)
	for {
		select {
		case <-t.ctx.Done():
			logger.Info("LLMMessageTransmitter loopSaveMsg done")
			return
		case msg := <-t.msgChan:
			t.saveMsg(msg)
		}
	}
}

func (t *LLMMessageTransmitter) saveMsg(msg *LLMMsgSaveRequest) {
	logger.Debug("LLMMessageTransmitter saveMsg", zap.Any("msg", msg))
	t.httpRequest.Body = msg
	_, err := t.httpRequest.DoRequest(t.ctx)
	if err != nil {
		logger.Error("LLMMessageTransmitter saveMsg error", zap.Error(err), zap.Any("msg", msg))
	}
}

func (t *LLMMessageTransmitter) OnLlmRequest(asrRes *proto.ASRResponse) error {
	if t.queryingMsg == nil {
		t.queryingMsg = &LLMMsgSaveRequest{
			DollId: t.dollId,
			Query:  "",
			Answer: "",
		}
		logger.Info("LLM save, start save a msg", zap.String("dollId", t.dollId))
	}
	t.queryingMsg.Query += asrRes.Text
	if asrRes.IsFinal {
		logger.Info("LLM save, queryingMsg is final", zap.Any("msg", t.queryingMsg))
		t.answeringMsg = t.queryingMsg
		t.queryingMsg = nil
	}
	return nil
}

func (t *LLMMessageTransmitter) OnLlmResponse(response *proto.LLMResponse) error {
	if t.answeringMsg == nil {
		logger.Warn("answeringMsg is nil, skip llm res", zap.Any("LLMResponse", response))
		return nil
	}
	if response.Content != "" && response.Content != "[NoResponse]" {
		t.answeringMsg.Answer += response.Content
	}
	if response.IsFinal {
		newMsg := t.answeringMsg
		t.answeringMsg = nil
		logger.Info("LLM save, answeringMsg is final", zap.Any("msg", newMsg), zap.Any("LLM", response))
		t.msgChan <- newMsg
	}
	return nil
}
