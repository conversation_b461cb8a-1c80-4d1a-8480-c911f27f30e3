package rtc

import (
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type LLMToolStateType int

const (
	LLMToolStateType_NO_TOOL LLMToolStateType = iota
	LLMToolStateType_TELL_STORY
	LLMToolStateType_PLAY_MUSIC
)

type MediaInfo struct {
	Xid       string `json:"xid"`
	Name      string `json:"name"`
	Url       string `json:"url"`
	Content   string `json:"content"`
	MediaType string `json:"media_type"`
}
type MediaInfoResp struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    MediaInfo `json:"data"`
}

type LLMToolState struct {
	baseUrl       string
	Index         int32
	SegmentIndex  int32
	State         LLMToolStateType
	Content       string
	ctx           context.Context
	MediaInfo     *MediaInfo
	mediaResBytes []byte
	RtcPCM        []byte
}

func LLMToolStateFromLLMResp(ctx context.Context, resp *proto.LLMResponse, baseUrl string) *LLMToolState {

	llmState := &LLMToolState{
		ctx:           ctx,
		baseUrl:       baseUrl,
		Index:         resp.MsgIndex,
		SegmentIndex:  resp.MsgSegmentIndex,
		State:         LLMToolStateType_NO_TOOL,
		Content:       "",
		MediaInfo:     nil,
		mediaResBytes: nil,
	}

	llmState.State = LLMToolStateType_NO_TOOL
	if v, ok := resp.ToolCall["name"]; ok {
		if v == "TellStory" {
			llmState.State = LLMToolStateType_TELL_STORY
			llmState.Content = resp.ToolCall["content"]
		} else if v == "PlayMusic" {
			llmState.State = LLMToolStateType_PLAY_MUSIC
			llmState.Content = resp.ToolCall["content"]
		}
	}

	return llmState
}

func (s *LLMToolState) IsNoTool() bool {
	return s.State == LLMToolStateType_NO_TOOL
}

func (s *LLMToolState) IsTellStory() bool {
	return s.State == LLMToolStateType_TELL_STORY
}

func (s *LLMToolState) IsPlayMusic() bool {
	return s.State == LLMToolStateType_PLAY_MUSIC
}
func (s *LLMToolState) Update() error {
	if s.IsNoTool() {
		return nil
	}

	err := s.fetchToolPayload()
	if err != nil {
		logger.Error("获取工具调用失败", zap.Error(err))
		return err
	}
	logger.Info("获取工具调用成功", zap.Any("MediaInfo", s.MediaInfo))

	err = s.convertAudioPCM()
	if err != nil {
		return err
	}
	return nil
}

func (s *LLMToolState) convertAudioPCM() error {
	if s.mediaResBytes == nil {
		return fmt.Errorf("媒体数据为空,跳过转换PCM")
	}

	processor, err := utils.NewAudioProcessor(s.mediaResBytes)
	if err != nil {
		return fmt.Errorf("创建音频处理器失败: %w", err)
	}

	pcmData, audioPCMFormat, err := processor.DecodeToPCM()
	if err != nil {
		return fmt.Errorf("解码PCM失败: %w", err)
	}

	dstFormat := utils.AudioPCMFormat{
		SampleRate: RtcPushAudioSampleRate, //
		Channels:   Channel,                // 单声道
		BitDepth:   SampleBitDepth,         // 16位
	}

	resampledPCM, err := utils.ResamplePCM(pcmData, audioPCMFormat, dstFormat)
	if err != nil {
		return fmt.Errorf("重采样PCM失败: %w", err)
	}

	s.RtcPCM = resampledPCM

	return nil
}

func (s *LLMToolState) fetchToolPayload() error {
	path := ""
	if s.IsTellStory() {
		path = "tell-story"
	} else if s.IsPlayMusic() {
		path = "play-music"
	} else {
		return fmt.Errorf("工具调用未实现")
	}
	url := fmt.Sprintf("%s/%s?name=%s", s.baseUrl, path, s.Content)

	respBody, err := service.DoRequest(s.ctx, "GET", url, nil, map[string]string{
		"Authorization": "b837o#G@j@8GbFH@",
	}, 10)
	if err != nil {
		return fmt.Errorf("请求媒体信息失败: %w", err)
	}
	var mediaInfoResp MediaInfoResp
	err = json.Unmarshal(respBody, &mediaInfoResp)
	if err != nil {
		return fmt.Errorf("解析媒体信息失败: %w", err)
	}
	s.MediaInfo = &mediaInfoResp.Data

	// fetch mp3、wav
	respBody, err = service.DownloadFile(s.ctx, s.MediaInfo.Url, map[string]string{}, 10, "")
	if err != nil {
		return fmt.Errorf("请求媒体数据失败: %w", err)
	}
	s.mediaResBytes = respBody

	return nil
}
