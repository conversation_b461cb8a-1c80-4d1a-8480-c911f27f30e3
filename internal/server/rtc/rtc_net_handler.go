package rtc

import (
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"
	"bytedance/bytertc/rtcengine"
	"time"

	"aigc_server/internal/service"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type AudioFrameBuffer struct {
	data      []byte
	timestamp time.Time
}

type RtcNetHandler struct {
	service.IGRPCClientHandler
	rtc           *RtcState
	frameBuffer   []AudioFrameBuffer
	bufferSize    int
	bufferTimeout time.Duration
	lastSendTime  time.Time
}

func NewRtcNetHandler(rtc *RtcState) *RtcNetHandler {
	return &RtcNetHandler{
		rtc:           rtc,
		frameBuffer:   make([]AudioFrameBuffer, 0),
		bufferSize:    3200,                   // bytes threshold
		bufferTimeout: 500 * time.Millisecond, // timeout
		lastSendTime:  time.Now(),
	}
}

func (h *RtcNetHandler) OnAudioFrame(audioFrame *rtcengine.IAudioFrame) error {
	// return h.rtc.OnAudioFrame(audioFrame)

	// TODO: 处理音频帧
	frame := *audioFrame
	data := frame.Data()

	sampleRate := frame.SampleRate()
	channel := frame.Channel()
	logger.Debug("NetHandler OnAudioFrame", zap.String("uid", h.rtc.uid), zap.Int("sampleRate", sampleRate), zap.Int("channel", channel))

	// Add frame to buffer
	h.frameBuffer = append(h.frameBuffer, AudioFrameBuffer{
		data:      data,
		timestamp: time.Now(),
	})

	// Calculate total buffer size
	totalSize := 0
	timeoutReached := false
	now := time.Now()

	for _, buf := range h.frameBuffer {
		totalSize += len(buf.data)
		// Check if any frame has timed out
		if now.Sub(buf.timestamp) >= h.bufferTimeout {
			timeoutReached = true
			break
		}
	}
	// Send if buffer size threshold reached or timeout occurred
	if totalSize >= h.bufferSize || timeoutReached || now.Sub(h.lastSendTime) >= h.bufferTimeout {
		if len(h.frameBuffer) > 0 {
			// Merge all frames data
			mergedData := make([]byte, 0, totalSize)
			for _, buf := range h.frameBuffer {
				mergedData = append(mergedData, buf.data...)
			}

			// Send merged data
			err := h.rtc.GrpcClient.SendASRAudio(mergedData)
			if err != nil {
				logger.Error("发送ASR请求失败", zap.Error(err))
				return err
			}
			logger.Debug("发送ASR请求", zap.String("uid", h.rtc.uid), zap.Int("frames", len(h.frameBuffer)), zap.Int("length", len(mergedData)))

			// Clear buffer
			h.frameBuffer = h.frameBuffer[:0]
			h.lastSendTime = now
		}
	}

	return nil
}

func (h *RtcNetHandler) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	// return h.rtc.OnAsrResponse(response)
	logger.Debug("OnAsrResponse", zap.String("uid", h.rtc.uid), zap.String("text", response.Text), zap.Bool("is_final", response.IsFinal))
	// check params
	if response.Text == "" {
		// logger.Error("ASR响应为空", zap.String("uid", h.rtc.uid))
		return nil
	}
	h.rtc.SetAsrInterrupt()
	if !response.IsFinal {
		return nil
	}
	err := h.rtc.GrpcClient.SendLLMMessage(response.Text, response.IsFinal)
	if err != nil {
		logger.Error("发送LLM请求失败", zap.Error(err))
		return err
	}
	h.rtc.LLMMessageTransmitter.OnLlmRequest(response)

	logger.Debug("发送LLM请求", zap.String("uid", h.rtc.uid), zap.String("content", response.Text), zap.Bool("is_final", response.IsFinal))
	return nil
}

func (h *RtcNetHandler) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	// return h.rtc.OnLlmResponse(response)
	logger.Debug("OnLlmResponse", zap.String("uid", h.rtc.uid), zap.String("toString", response.String()))

	err := h.rtc.CheckLlmCmd(ctx, response)
	if err != nil {
		logger.Error("检查LLM命令失败", zap.Error(err))
		return err
	}
	h.rtc.LLMMessageTransmitter.OnLlmResponse(response)
	if response.Content == "" || response.Type != proto.LLMRespType_NORMAL || response.Content == "[NoResponse]" {
		// logger.Error("LLM响应为空", zap.String("uid", h.rtc.uid))
		logger.Info("LLM响应跳过TTS", zap.Any("LLMResponse", response))

		// 如果LLM响应是重置，则发送重置TTS请求
		if response.Type == proto.LLMRespType_RESET {
			err = h.rtc.GrpcClient.SendTTSText("", response.MsgIndex, response.MsgSegmentIndex, "", true, response.IsFinal)
			if err != nil {
				logger.Error("TTS RESET 请求失败", zap.Error(err))
				return err
			}
			return nil
		}
		return nil
	}

	err = h.rtc.GrpcClient.SendTTSText(response.Content, response.MsgIndex, response.MsgSegmentIndex, "", false, response.IsFinal)
	if err != nil {
		logger.Error("TTS请求失败", zap.Error(err))
		return err
	}

	logger.Debug("TTS请求成功", zap.String("uid", h.rtc.uid), zap.String("content", response.Content))
	return nil
}
func (h *RtcNetHandler) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	// return h.rtc.OnTtsResponse(response)
	logger.Info("OnTtsResponse", zap.String("uid", h.rtc.uid), zap.Int("audio_size", len(response.Audio)), zap.Int32("msg_index", response.MsgIndex))

	err := h.rtc.OnTTSResult(response.Audio, response.MsgIndex, response.MsgSegmentIndex, response.IsFinal)
	if err != nil {
		logger.Error("TTS结果处理失败", zap.Error(err))
		return err
	}
	return nil
}
