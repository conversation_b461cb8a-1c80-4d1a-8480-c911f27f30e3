package rtc

import (
	"aigc_server/internal/config"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"fmt"
	"sync"
	"time"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

const AsrSampleRate = 16000
const TtsRecordSampleRate = 16000

// callback handler（如果你的callback 中embeding了默认回调实现，则只需要关注实现自己需要的回调； 否则，应该实现全部回调）
type rtcEngineEventHandler struct {
	rtcengine.IRTCEngineEventHandlerDefaultImp
}

type rtcRoomEventHandler struct {
	rtcengine.IRTCRoomEventHandlerDefaultImpl
	rtc *RtcState
}

type audioFrameObserver struct {
	rtcengine.IAudioFrameObserverDefaultImp
	oncePrintRemoteAudioFrame sync.Once
	rtc                       *RtcState
}

func (h *rtcRoomEventHandler) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {
	logger.Info("OnRoomStateChanged",
		zap.String("roomID", roomId),
		zap.String("uid", uid),
		zap.Int("state", state),
		zap.String("extraInfo", extraInfo),
	)
	if uid != h.rtc.uid {
		return
	}
	if state == 0 {
		logger.Info("OnRoomStateChanged join room success")
	} else {
		logger.Info("OnRoomStateChanged join room failed",
			zap.Int("state", state),
		)
	}
}

func (h *rtcRoomEventHandler) OnUserJoined(userInfo rtcengine.UserInfo) {
	logger.Info("OnUserJoined",
		zap.String("uid", userInfo.UID),
		zap.String("extraInfo", userInfo.ExtraInfo),
	)
	if userInfo.UID != h.rtc.uid {
		return
	}
	h.rtc.userJoinedStatus = 1
}
func (h *rtcRoomEventHandler) OnUserLeave(uid string, reason rtcengine.UserOfflineReason) {
	logger.Info("OnUserLeave",
		zap.String("uid", uid),
		zap.Int("reason", int(reason)),
	)
	if uid != h.rtc.uid {
		return
	}
	h.rtc.userJoinedStatus = 2
	// close(h.rtc.audioFrameChan)
	h.rtc.CtxCancel()
}
func (h *audioFrameObserver) OnRemoteUserAudioFrame(streamInfo rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	h.oncePrintRemoteAudioFrame.Do(func() {
		logger.Info("OnRemoteUserAudioFrame",
			zap.String("uid", streamInfo.UserID),
			zap.String("roomID", streamInfo.RoomID),
			zap.Int("streamIndex", streamInfo.StreamIndex),
			zap.Int64("audioFrameTimestamp", audioFrame.TimestampUs()),
			zap.Int("audioFrameSampleRate", audioFrame.SampleRate()),
			zap.Int("audioFrameChannel", audioFrame.Channel()),
			zap.String("first10Bytes", string(audioFrame.Data()[:10])),
		)
	})
	if streamInfo.UserID != h.rtc.uid {
		return
	}

	targetSampleRate := AsrSampleRate
	targetChannel := 1

	srcFormat := utils.AudioPCMFormat{
		SampleRate: audioFrame.SampleRate(),
		Channels:   audioFrame.Channel(),
		BitDepth:   16,
	}
	dstFormat := utils.AudioPCMFormat{
		SampleRate: targetSampleRate,
		Channels:   targetChannel,
		BitDepth:   SampleBitDepth,
	}
	bytes, err := utils.ResamplePCM(audioFrame.Data(), srcFormat, dstFormat)
	if err != nil {
		logger.Error("Resamples error", zap.Error(err))
		return
	}

	// TODO: audioFrame 只在回调函数体内有效, 需要拷贝转发到另一个协程异步处理
	audioFrameBuilder := rtcengine.AudioFrameBuilder{
		SampleRate: targetSampleRate,
		Channel:    targetChannel,
		Data:       bytes,
	}
	copiedAudioFrame := rtcengine.BuildAudioFrame(audioFrameBuilder)
	// h.rtc.OnAudioFrame(&copiedAudioFrame)
	for {
		select {
		case h.rtc.audioFrameChan <- copiedAudioFrame:
			return
		default:
			logger.Info("OnRemoteUserAudioFrame. audio frame channel is full, drop audio frame")
			return
		}
	}
}

type RtcState struct {
	cfg       *config.Config
	roomID    string
	uid       string
	serverUid string

	engine           rtcengine.IRTCEngine
	room             rtcengine.IRTCRoom
	userJoinedStatus int // 0: 未加入房间, 1: 加入房间, 2: 离开房间
	GrpcClient       *service.GRPCClient
	netHandler       *RtcNetHandler

	audioFrameChan chan rtcengine.IAudioFrame

	ctx       context.Context
	CtxCancel context.CancelFunc

	LLMMessageTransmitter *LLMMessageTransmitter

	audioInterruptIndex   int32
	interruptPauseChan    chan struct{}
	voiceFrameSourceQueue []*VoiceFrameSource
	mutexFrameSourceQueue sync.Mutex
}

func NewRtcStateAndStart(cfg *config.Config, uid, roomID string, grpcClient *service.GRPCClient) (*RtcState, error) {
	serverUid := utils.GetRTCServerUserId(uid)
	instance := &RtcState{cfg: cfg, roomID: roomID, uid: uid, serverUid: serverUid, GrpcClient: grpcClient}
	err := instance.RoomStart()
	if err != nil {
		logger.Error("启动RTC房间失败", zap.Error(err))
		instance.RoomStop()
		return nil, err
	}
	return instance, nil
}

func (s *RtcState) RoomStart() error {
	logger.Info("RTCService starting", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
	// config

	s.ctx, s.CtxCancel = context.WithCancel(context.Background())
	s.audioFrameChan = make(chan rtcengine.IAudioFrame, 100)
	s.voiceFrameSourceQueue = make([]*VoiceFrameSource, 0)

	appID := s.cfg.Rtc.AppID
	appKey := s.cfg.Rtc.AppKey

	engineParameters := make(map[string]interface{})
	engineConfig := &rtcengine.EngineConfig{AppID: appID, Parameters: engineParameters} // 字符串形式
	engineEventHandler := &rtcEngineEventHandler{}

	// create engine
	engine := rtcengine.CreateRTCEngine(engineConfig, engineEventHandler)
	logger.Info("RTC engine creating",
		zap.String("appID", appID),
		zap.Any("parameters", engineParameters),
		zap.Any("handler", engineEventHandler),
	)

	// join room
	room := engine.CreateRTCRoom(s.roomID)
	roomEventHandler := &rtcRoomEventHandler{rtc: s}
	ok := room.SetRTCRoomEventHandler(roomEventHandler)
	if ok != 0 {
		// panic("set room event handler api call error")
		return fmt.Errorf("set room event handler api call error")
	}
	roomToken, _, err := utils.GenerateRoomToken(appID, appKey, s.roomID, s.serverUid)
	if err != nil {
		return fmt.Errorf("generate room token error: %v", err)
	}
	userInfo := &rtcengine.UserInfo{UID: s.serverUid, ExtraInfo: "client_uid:" + s.uid}
	roomConfig := &rtcengine.RTCRoomConfig{IsPublishAudio: true, IsPublishVideo: false, IsAutoSubscribeAudio: true, IsAutoSubscribeVideo: false}
	ok = room.JoinRoom(roomToken, userInfo, roomConfig)
	if ok != 0 {
		// panic("join room api call error")
		return fmt.Errorf("join room api call error")
	}
	logger.Info("RTC room joined",
		zap.String("roomID", s.roomID),
		zap.String("uid", s.uid),
		zap.String("serverUid", s.serverUid),
		zap.String("token", roomToken),
	)

	s.room = room
	s.engine = engine
	// audioFrameChan := make(chan rtcengine.IAudioFrame)
	audioFrameObserver := &audioFrameObserver{rtc: s}
	// register audio frame observer
	ok = engine.RegisterAudioFrameObserver(audioFrameObserver)
	if ok != 0 {
		// panic("register audio frame observer api call error")
		return fmt.Errorf("register audio frame observer api call error")
	}
	audioFormat := &rtcengine.AudioFormat{
		SampleRate:     AsrSampleRate,
		Channel:        rtcengine.AudioChannelMono,
		SamplesPerCall: 0}
	ok = engine.EnableAudioFrameCallback(rtcengine.AudioFrameCallbackMethodRemoteUser, audioFormat)
	if ok != 0 {
		// panic("register audio frame observer api call error, error code" + strconv.Itoa(ok))
		return fmt.Errorf("register audio frame observer api call error, error code %d", ok)
	}

	// push external audio frame
	engine.SetAudioSourceType(rtcengine.AudioSourceTypeExternal)

	s.LLMMessageTransmitter = NewLLMMessageTransmitter(s.ctx, s.cfg, s.uid)

	return nil
}
func (s *RtcState) RoomStop() error {
	logger.Info("RTCService stopping", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
	s.CtxCancel()
	if s.room != nil {
		s.room.Destroy()
		s.room = nil
	}
	if s.audioFrameChan != nil {
		close(s.audioFrameChan)
		s.audioFrameChan = nil
	}
	s.netHandler = nil
	s.engine = nil
	rtcengine.DestroyRTCEngine()
	return nil
}

func (s *RtcState) RoomLooping() error {
	defer func() {
		if err := recover(); err != nil {
			logger.RecoverWithStack(err)
		}
	}()
	defer s.RoomStop()

	timeout, cancelTimeout := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancelTimeout()
	s.userJoinedStatus = 0
	go func() {
		<-timeout.Done()
		if s.userJoinedStatus == 0 {
			logger.Error("RTCService RoomLooping timeout, user not joined room", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
			s.CtxCancel()
		}
	}()

	s.netHandler = NewRtcNetHandler(s)
	s.GrpcClient.SetHandler(s.netHandler)
	defer s.GrpcClient.SetHandler(nil)

	go s.loopReceiveAudioFrame()
	go s.loopDispatchAudioFrame()

	<-s.ctx.Done()

	logger.Info("RTCService ctx cancel", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
	return nil
}

func (s *RtcState) loopReceiveAudioFrame() error {
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx cancel, stop receive audio frame", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
			return nil
		case frame := <-s.audioFrameChan:
			if err := s.netHandler.OnAudioFrame(&frame); err != nil {
				logger.Error("RTCService RoomLooping AudioFrame Receiver error", zap.Error(err))
			}
		}
	}
}

func (s *RtcState) loopDispatchAudioFrame() error {
	sendAudioChan := make(chan rtcengine.IAudioFrame)
	defer close(sendAudioChan)

	logger.Info("Start goroutine to push external audio frame", zap.Int("frame", FrameRate))

	go s.frameProducer(sendAudioChan)
	go s.frameConsumer(sendAudioChan)

	<-s.ctx.Done()
	return nil
}
func (s *RtcState) frameProducer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer func() {
		if err := recover(); err != nil {
			logger.RecoverWithStack(err)
		}
	}()
	frameCount := 0
	for {
		select {
		case <-s.ctx.Done():
			return nil
		default:
			if s.interruptPauseChan != nil {
				<-s.interruptPauseChan
			}
			source := s.GetFirstVoiceFrameSource()
			var audioFrame rtcengine.IAudioFrame
			if source != nil {
				frameCount = 0
				var err error
				audioFrame, _, err = source.NextFrame()
				if err != nil {
					logger.Error("RTCService RoomLooping AudioFrame Producer error", zap.Error(err))
					continue
				}
			} else {
				if frameCount < FrameRate*2 {
					audioFrame = VoiceFrameSourceEmptyFrame()
					frameCount++
				} else {
					time.Sleep(time.Millisecond * 10)
					continue
				}
			}
			select {
			case <-s.ctx.Done():
				return nil
			case sendAudioChan <- audioFrame:
			}
		}
	}
}

func (s *RtcState) frameConsumer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer func() {
		if err := recover(); err != nil {
			logger.RecoverWithStack(err)
		}
	}()

	ticker := time.Tick(time.Millisecond * time.Duration(1000/FrameRate))
	for range ticker {
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx cancel, stop send audio frame", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid))
			return nil
		case audioFrame := <-sendAudioChan:
			go func() {
				defer func() {
					if err := recover(); err != nil {
						logger.Warn("RTCService push external audio frame panic")
					}
				}()
				ret := s.engine.PushExternalAudioFrame(audioFrame)
				if ret != 0 {
					logger.Error("RTCService push external audio frame api call error, error code %d", zap.Int("error code", ret))
				}
			}()
		default:
			// logger.Info("RTCService audio consumer chan is empty", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid), zap.Time("time", t))
		}
	}
	return nil
}
func (s *RtcState) OnTTSResult(audioDatas []byte, index int32, segment_index int32, isFinal bool) error {
	audio8k, err := utils.ResamplePCM(audioDatas, utils.AudioPCMFormat{
		SampleRate: TtsRecordSampleRate,
		Channels:   1,
		BitDepth:   16,
	}, utils.AudioPCMFormat{
		SampleRate: RtcPushAudioSampleRate,
		Channels:   Channel,
		BitDepth:   SampleBitDepth,
	})
	if err != nil {
		logger.Error("ResamplePCM error", zap.Error(err))
		return err
	}
	source := NewVoiceFrameSource(audio8k, LLMToolStateType_NO_TOOL, index, segment_index)
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()
	s.voiceFrameSourceQueue = append(s.voiceFrameSourceQueue, source)
	return nil
}

func (s *RtcState) CheckLlmCmd(ctx context.Context, response *proto.LLMResponse) error {

	if response.Type == proto.LLMRespType_RESET {
		s.SetAudioInterruptIndex(response.MsgIndex, response.MsgSegmentIndex)
		s.SetAsrResume()

	} else if response.Type == proto.LLMRespType_RESUME {
		s.SetAsrResume()
	} else if response.Type == proto.LLMRespType_NORMAL {
		go s.UpdateLLMToolState(s.ctx, response, s.cfg.HTTPClient.MediaBaseURL)
	}
	return nil
}
func (s *RtcState) UpdateLLMToolState(ctx context.Context, response *proto.LLMResponse, baseUrl string) error {
	llmToolState := LLMToolStateFromLLMResp(ctx, response, baseUrl)
	if llmToolState.IsNoTool() {
		return nil
	}
	curSource := s.GetFirstVoiceFrameSource()
	if curSource != nil && curSource.LLMVoiceType == llmToolState.State &&
		curSource.LLMIndex == llmToolState.Index {
		return nil
	}
	logger.Info("UpdateLLMToolState Start", zap.Any("LLMResponse", response))
	if err := llmToolState.Update(); err != nil {
		logger.Error("UpdateLLMToolState error", zap.Error(err))
		return err
	}
	if llmToolState.RtcPCM == nil {
		logger.Error("RtcStateUpdateLLMToolState error, AudioPCM is nil", zap.Any("llmToolState", llmToolState))
		return fmt.Errorf("AudioPCM is nil")
	}
	logger.Info("RtcState UpdateLLMToolState. new tool call source", zap.Any("llmToolState.State", llmToolState.State),
		zap.Int("llmToolState.Index", int(llmToolState.Index)),
		zap.Any("MediaInfo", llmToolState.MediaInfo))
	source := NewVoiceFrameSource(llmToolState.RtcPCM, llmToolState.State, llmToolState.Index, llmToolState.SegmentIndex)
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()
	s.voiceFrameSourceQueue = append(s.voiceFrameSourceQueue, source)
	return nil
}

func (s *RtcState) GetFirstVoiceFrameSource() *VoiceFrameSource {
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()

	for len(s.voiceFrameSourceQueue) > 0 {
		source := s.voiceFrameSourceQueue[0]
		if source == nil || source.IsDone() || source.TestInterrupt(s.audioInterruptIndex) {
			s.voiceFrameSourceQueue = s.voiceFrameSourceQueue[1:]
			continue
		}
		return source
	}
	return nil
}

func (s *RtcState) SetAudioInterruptIndex(index int32, seqment_index int32) {
	s.audioInterruptIndex = index
	logger.Info("RtcState LLM Reset SetAudioInterruptIndex",
		zap.Int("index", int(index)),
		zap.Int("seqment_index", int(seqment_index)))
}

func (s *RtcState) SetAsrInterrupt() error {
	if source := s.GetFirstVoiceFrameSource(); source != nil {
		if !source.TestAsrInterrupt() {
			logger.Info("RtcState SetAsrInterrupt skip. source.LLMVoiceType:", zap.Any("llmVoiceType", source.LLMVoiceType))
			return nil
		}
		logger.Info("RtcState SetAsrInterrupt. set asr interrupt", zap.Any("llm", map[string]interface{}{"LLMIndex": source.LLMIndex, "LLMSegmentIndex": source.LLMSegmentIndex, "LLMVoiceType": source.LLMVoiceType, "FrameIndex": source.FrameIndex, "FrameSize": source.FrameSize, "len": source.len}))
	}
	if s.interruptPauseChan == nil {
		logger.Info("RtcState interruptPauseChan is nil, create it")
		s.interruptPauseChan = make(chan struct{})
	}
	return nil
}

func (s *RtcState) SetAsrResume() error {
	if s.interruptPauseChan != nil {
		logger.Info("RtcState SetAsrResume. interruptPauseChan is not nil, close it")
		close(s.interruptPauseChan)
		s.interruptPauseChan = nil
	}
	return nil
}
