package rtc

import (
	"bytedance/bytertc/rtcengine"
	"fmt"
)

const RtcPushAudioSampleRate = 8000
const Channel = 1
const SampleBitDepth = 16
const FrameRate = 50

type VoiceFrameSource struct {
	AudioBytes      []byte
	LLMVoiceType    LLMToolStateType
	LLMIndex        int32
	LLMSegmentIndex int32
	SampleRate      int
	Channel         int
	SampleBitDepth  int
	FrameRate       int

	FrameSize  int
	FrameIndex int
	len        int
}

func NewVoiceFrameSource(audioBytes []byte, llmToolStateType LLMToolStateType, llmIndex int32, llmSegmentIndex int32) *VoiceFrameSource {
	frameSize := Channel * RtcPushAudioSampleRate * SampleBitDepth / 8 / FrameRate
	return &VoiceFrameSource{
		AudioBytes:      audioBytes,
		LLMVoiceType:    llmToolStateType,
		LLMIndex:        llmIndex,
		LLMSegmentIndex: llmSegmentIndex,
		SampleRate:      RtcPushAudioSampleRate,
		Channel:         Channel,
		SampleBitDepth:  SampleBitDepth,
		FrameRate:       FrameRate,
		FrameSize:       frameSize,
		FrameIndex:      0,
		len:             len(audioBytes),
	}
}
func (v *VoiceFrameSource) TestInterrupt(interruptIndex int32) bool {
	return v.LLMIndex >= 0 && v.LLMIndex < interruptIndex
}
func (v *VoiceFrameSource) TestAsrInterrupt() bool {
	return v.LLMVoiceType != LLMToolStateType_TELL_STORY
}
func (v *VoiceFrameSource) NextFrame() (rtcengine.IAudioFrame, int, error) {
	if v.IsDone() {
		return nil, v.FrameIndex, fmt.Errorf("no more frames")
	}
	iStart := v.FrameIndex * v.FrameSize
	iEnd := min(iStart+v.FrameSize, v.len)
	frame := rtcengine.AudioFrameBuilder{
		SampleRate: v.SampleRate,
		Channel:    v.Channel,
		Data:       v.AudioBytes[iStart:iEnd],
	}
	v.FrameIndex++
	return rtcengine.BuildAudioFrame(frame), v.FrameIndex, nil
}
func (v *VoiceFrameSource) IsDone() bool {
	return v.FrameIndex*v.FrameSize >= v.len
}

var emptyFrameData []byte

func VoiceFrameSourceEmptyFrame() rtcengine.IAudioFrame {
	if emptyFrameData == nil {
		emptyFrameData = make([]byte, Channel*RtcPushAudioSampleRate*SampleBitDepth/8/FrameRate)
		for i := range emptyFrameData {
			if i%2 == 0 {
				emptyFrameData[i] = 0x80
			} else {
				emptyFrameData[i] = 0x00
			}
		}
	}
	return rtcengine.BuildAudioFrame(rtcengine.AudioFrameBuilder{
		SampleRate: RtcPushAudioSampleRate,
		Channel:    Channel,
		Data:       emptyFrameData,
	})
}
