package shared

// 共享的类型定义和常量

// ProcessStatus 进程状态
type ProcessStatus int

const (
	ProcessStatusStopped ProcessStatus = iota
	ProcessStatusStarting
	ProcessStatusRunning
	ProcessStatusStopping
)

// ServiceType 服务类型
type ServiceType int

const (
	ServiceTypeASR ServiceType = iota
	ServiceTypeLLM
	ServiceTypeTTS
	ServiceTypeRTC
)

// EventType 事件类型
type EventType int

const (
	EventTypeASRResult EventType = iota
	EventTypeLLMResult
	EventTypeTTSResult
	EventTypeRTCStateChange
)
