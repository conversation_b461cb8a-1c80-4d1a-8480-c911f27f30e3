package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/server/http"
	"aigc_server/internal/server/rtc"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env     = flag.String("env", "prod", "运行环境: prod")
	isChild = flag.Bool("child", false, "是否为子进程")
	uid     = flag.String("uid", "", "用户ID（子进程参数）")
	roomID  = flag.String("room_id", "", "房间ID（子进程参数）")
	// roomToken = flag.String("room_token", "", "房间Token（子进程参数）")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"

	// 子进程引用
	childProcesses = make(map[string]*os.Process)
	childMutex     = &sync.Mutex{}
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&logger.Config{
		Level:    cfg.Log.Level,
		Format:   cfg.Log.Format,
		Output:   cfg.Log.Output,
		FilePath: cfg.Log.FilePath,
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 记录启动信息
	logger.Info("服务启动",
		zap.String("name", cfg.Server.Name),
		zap.String("version", Version),
		zap.String("build_time", BuildTime),
		zap.String("git_commit", GitCommit),
		zap.String("env", *env),
		zap.Bool("is_child", *isChild),
		zap.String("uid", *uid),
		zap.String("room_id", *roomID),
	)

	// 根据进程类型执行不同的逻辑
	if *isChild {
		if err := runRTCState(cfg); err != nil {
			logger.Error("RTC状态运行失败", zap.Error(err))
			os.Exit(1)
		}
	} else {
		// 主进程：启动HTTP服务器并fork子进程
		runMainProcess(cfg)
	}
}

func runRTCState(cfg *config.Config) error {
	// 检查必要的参数
	if *uid == "" || *roomID == "" {
		logger.Error("缺少必要的参数",
			zap.String("uid", *uid),
			zap.String("room_id", *roomID),
		)
		return fmt.Errorf("缺少必要的参数uid, room_id")
	}

	// 处理信号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个done通道用于优雅关闭
	done := make(chan struct{})
	defer close(done)

	// 信号处理
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("子进程接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	logger.Info("开始运行RTC状态", zap.String("uid", *uid), zap.String("room_id", *roomID))

	// 创建GRPC客户端
	grpcClient, err := service.NewGRPCClient(cfg, *uid)
	if err != nil {
		logger.Error("创建GRPC客户端失败", zap.Error(err))
		return err
	}

	// 创建RTC状态
	rtcState, err := rtc.NewRtcStateAndStart(cfg, *uid, *roomID, grpcClient)
	if err != nil {
		logger.Error("创建RTC房间失败", zap.Error(err))
		grpcClient.Close() // 确保关闭GRPC客户端
		return err
	}

	// 启动RTC循环
	go func() {
		if err := rtcState.RoomLooping(); err != nil {
			logger.Error("RTC状态运行失败", zap.Error(err))
		}
		cancel() // 发生错误时取消上下文
	}()

	// 等待退出信号
	<-ctx.Done()

	// 优雅关闭
	logger.Info("开始关闭子进程资源...")

	// 设置关闭超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// 创建一个通道用于等待清理完成
	cleanup := make(chan struct{})
	go func() {
		// 按顺序关闭资源
		rtcState.CtxCancel()
		grpcClient.Close()
		close(cleanup)
	}()

	// 等待清理完成或超时
	select {
	case <-cleanup:
		logger.Info("子进程资源已清理完成")
	case <-shutdownCtx.Done():
		logger.Warn("子进程资源清理超时")
	}

	logger.Info("RTC状态运行结束")
	return nil
}

// runMainProcess 运行主进程
func runMainProcess(cfg *config.Config) {
	// 创建HTTP服务器，传入启动子进程的回调函数
	mainHttpServer := http.NewMainHTTPServer(cfg, startRtcProcess)

	// 处理信号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("主进程接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	// 启动HTTP服务器
	go func() {
		logger.Info("启动HTTP服务器（主进程）",
			zap.String("host", cfg.HTTP.Host),
			zap.Int("port", cfg.HTTP.Port),
		)
		if err := mainHttpServer.Start(); err != nil {
			logger.Error("HTTP服务器退出", zap.Error(err))
			cancel()
		}
	}()

	// 关闭所有子进程
	defer closeAllChildProcesses()

	// 等待退出信号
	<-ctx.Done()

	// 优雅关闭
	if err := mainHttpServer.Stop(); err != nil {
		logger.Error("HTTP服务器关闭失败", zap.Error(err))
	}
	logger.Info("HTTP服务器已关闭（主进程）")

}

// closeAllChildProcesses 关闭所有子进程
func closeAllChildProcesses() {
	logger.Info("正在关闭所有子进程...")

	childMutex.Lock()
	processes := make(map[string]*os.Process)
	for id, process := range childProcesses {
		processes[id] = process
	}
	childMutex.Unlock()

	// 使用WaitGroup来等待所有子进程关闭
	var wg sync.WaitGroup
	for id, process := range processes {
		wg.Add(1)
		go func(id string, process *os.Process) {
			defer wg.Done()

			logger.Info("正在关闭子进程",
				zap.String("id", id),
				zap.Int("pid", process.Pid),
			)

			if err := utils.CloseProcess(process.Pid, 5*time.Second); err != nil {
				logger.Error("关闭子进程失败",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
					zap.Error(err),
				)
			} else {
				logger.Info("子进程已关闭",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
				)
			}

			// 从映射中移除子进程
			childMutex.Lock()
			delete(childProcesses, id)
			childMutex.Unlock()
		}(id, process)
	}

	// 等待所有子进程处理完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 设置总体超时时间
	select {
	case <-done:
		logger.Info("所有子进程已关闭")
	case <-time.After(10 * time.Second):
		logger.Error("部分子进程可能未正常关闭")
	}
}

// startRtcProcess 启动子进程的回调函数
func startRtcProcess(uid, roomID string) error {
	// 生成唯一的子进程ID
	processKey := utils.GetProcessKey(uid)

	// 检查是否已经有相同ID的子进程
	childMutex.Lock()
	if _, exists := childProcesses[processKey]; exists {
		logger.Info("子进程已存在，关闭",
			zap.String("processKey", processKey),
			zap.String("uid", uid))
		// 如果子进程已存在，关闭子进程
		err := utils.CloseProcess(childProcesses[processKey].Pid, 5*time.Second)
		if err != nil {
			// childMutex.Unlock()
			logger.Error("关闭子进程Error", zap.Error(err))
			// return err
		}
		delete(childProcesses, processKey)
	}
	childMutex.Unlock()
	// 启动新的子进程
	// 获取当前可执行文件路径
	execPath, err := os.Executable()
	if err != nil {
		logger.Error("获取可执行文件路径失败", zap.Error(err))
		return err
	}

	// 构建子进程命令
	cmd := exec.Command(
		execPath,
		"--env", *env,
		"--child",
		"--uid", uid,
		"--room_id", roomID,
	)

	// 设置子进程输出
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 启动子进程
	logger.Info("正在启动子进程...",
		zap.String("path", execPath),
		zap.String("processKey", processKey),
		zap.String("uid", uid),
		zap.String("room_id", roomID),
	)
	if err := cmd.Start(); err != nil {
		logger.Error("启动子进程失败", zap.Error(err))
		return err
	}

	logger.Info("子进程已启动",
		zap.Int("pid", cmd.Process.Pid),
		zap.String("processKey", processKey),
	)

	// 保存子进程信息
	childMutex.Lock()
	childProcesses[processKey] = cmd.Process
	childMutex.Unlock()

	// 不等待子进程结束，让它在后台运行
	go func() {
		if err := cmd.Wait(); err != nil {
			logger.Error("子进程异常退出",
				zap.Error(err),
				zap.String("processKey", processKey),
			)
		}

		// 子进程结束后从映射中移除
		childMutex.Lock()
		delete(childProcesses, processKey)
		childMutex.Unlock()
	}()

	return nil
}
