
// Doll99 API
// This file contains HTTP requests for the Doll99 API.
// The API is used to manage dolls and their information.

@BaseUrl=http://115.190.73.213:8912
# @BaseUrl=https://ap.fifthday.bid
@Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJkMGplMHEzbjRjdGJqajFrcjNtZyJ9.oGCmRK1PWWa8surJ2vcsOijjIl0vqumpZq89yoB0umY

@DollID=zl
###

// 请求验证码
POST {{BaseUrl}}/app/login/get-verifycode
Content-Type: application/json

{
  "phone": "18600000000"
}

###

// 验证验证码
POST {{BaseUrl}}/app/login/verify-verifycode
Content-Type: application/json

{
  "phone": "18600000000",
  "verifyCode": "6470"
}

###

// 刷新 token
POST {{BaseUrl}}/app/refresh-token
Content-Type: application/json
Authorization: Bearer {{Token}}

{
}

### 
// 绑定小玩偶
POST {{BaseUrl}}/app/doll/bind
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 获取小玩偶详情列表
POST {{BaseUrl}}/app/doll/infos
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 存储聊天内容
POST {{BaseUrl}}/doll/msg/save
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "query": "我今天不开心",
  "answer": "我陪你一起玩吧"
}

###

// 获取聊天内容
POST {{BaseUrl}}/app/msg/get-doll
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "limit": 1,
  "timestamp": 0,
  "op": "before"
}

###

// 获取聊天总结
POST {{BaseUrl}}/app/msg/get-summary
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 获取角色列表
POST {{BaseUrl}}/app/role/get-role
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

###

// 切换角色
POST {{BaseUrl}}/app/role/change
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "newRoleId": "r_2"
}

###

// 删除角色
POST {{BaseUrl}}/app/role/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "roleId": "{{DollID}}_d0gvbi3n4ct31vgt7mig"
}

###

// 获取硬件状态（电量、音量等）
POST {{BaseUrl}}/app/doll/get-state
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661"
}

### 

// 一键生成角色内容
POST {{BaseUrl}}/app/role/gen
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661"
}

###

// 创建/修改自定义角色
POST {{BaseUrl}}/app/role/upsert
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "661",
  "roleId": "",
  "roleContent": "我是一个智慧的故事讲述者",
  "timbreId": "original_1"
}

###

// 获取音色列表
POST {{BaseUrl}}/app/timbre/list
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 删除音色
POST {{BaseUrl}}/app/timbre/delete
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "timbreId": "t_001"
}

###

// 录制音色（voice 为 base64 编码音频，最长 20 秒）
POST {{BaseUrl}}/app/timbre/record
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "voice": "BASE64_ENCODED_AUDIO"
}

###

// 家长助手聊天
GET {{BaseUrl}}/app/msg/chat-parent-sse?dollId={{DollID}}&queryType=text&query=孩子今天在干嘛？
Content-Type: application/json
Authorization: Bearer {{Token}}

###

// 获取家长助手消息记录
POST {{BaseUrl}}/app/msg/get-parent
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "offset": 0,
  "limit": 20
}

###

// 获取订阅链接
POST {{BaseUrl}}/app/setting/subscribe
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 获取知识库导入链接
POST {{BaseUrl}}/app/setting/repository
Content-Type: application/json
Authorization: Bearer {{Token}}

{}


###

// 退出登录
POST {{BaseUrl}}/app/setting/quit-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

###

// 注销账号
POST {{BaseUrl}}/app/setting/delete-account
Content-Type: application/json
Authorization: Bearer {{Token}}

{}

// 排行榜
POST {{BaseUrl}}/app/rank/get-rank
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "type": ["total", "idioms", "english", "voice", "puzzle", "wisdom"]
}

# {
#   "code": 0,
#   "message": "success",
#     "data": {
#       "rank": [
#       {
#         "type": "total",
#         "friends": [
#           {
#             "rank": 1,
#             "nickname": "张三",
#             "score": 100,
#             "isSelf": true
#           },
#           {
#             "rank": 1,
#             "nickname": "张三",
#             "score": 100
#           }
#         ],
#         "area": [
#           {
#             "rank": 1,
#             "nickname": "张三",
#             "score": 100,
#             "isSelf": true
#           },
#           {
#             "rank": 1,
#             "nickname": "张三",
#             "score": 100
#           }
#         ]
#       }]
#     }
# }

###

// 家长助手提示词
POST {{BaseUrl}}/app/msg/get-parent-prompts
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "prompts":[
#       {
#         "id": 1,
#         "prompt": "你好，我是家长助手，有什么可以帮你的吗？"
#       }
#     ] 
#   }
# }

###

// 修改小朋友信息
POST {{BaseUrl}}/app/child/update-info
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "name": "小明",
  "birthday": "2020-01-01"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "name": "小明",
#     "birthday": "2020-01-01"
#   }
# }

###

// 获取小朋友信息
POST {{BaseUrl}}/app/child/get-info
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "name": "小明",
#     "birthday": "2020-01-01"
#   }
# }

###

// 游戏详细内容列表
POST {{BaseUrl}}/app/game/get-game-list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}"
}

{
  "code": 0,
  "message": "success",
  "data": {
    "gameList": [
      {
        "id": 1,
        "name": "成语接龙",
        "group": "idioms",
        "progress":10
      },
      {
        "id": 2,
        "name": "半词接龙",
        "group": "idioms",
        "progress":10
      },
      {
        "id": 3,
        "name": "英语跟读",
        "group": "english",
        "progress":10
      },
      {
        "id": 4,
        "name": "猜谜",
        "group": "puzzle",
        "progress":10
      },
      {
        "id": 5,
        "name": " 听声识别",
        "group": "voice",
        "progress":10
      },
      {
        "id": 6,
        "name": "智慧大脑",
        "group": "wisdom",
        "progress":10
      }
    ]
  }
}

###

// 获取游戏详细内容
POST {{BaseUrl}}/app/game/get-stage-list
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "gameId": 1
}

{
  "code": 0,
  "message": "success",
  "data": {
    "curStageId": 1,
    "stages": [
      {
        "id": 1,
        "name": "等级1",
        "desc": "成语启蒙",
        "award": 2,
        "step": {
          "totalCount": 10,
          "finCount": 1,
          "lastStepState": 0, // 0: 未解锁 1: 已解锁 2: 成功 3: 失败
        },
      },
      {
        "id": 2,
        "name": "等级2",
        "desc": "成语挑战",
        "award": 2,
        "step": {
          "totalCount": 10,
          "finCount": 1,
          "lastStepState": 0, // 0: 未解锁 1: 已解锁 2: 成功 3: 失败
        },
      },
    ]
  }
}

###

// 获取游戏stage详情
POST {{BaseUrl}}/app/game/get-stage-detail
Content-Type: application/json
Authorization: Bearer {{Token}}

{
  "dollId": "{{DollID}}",
  "gameId": 1,
  "stageId": 1
}

# {
#   "code": 0,
#   "message": "success",
#   "data": {
#     "infoTemplate": "你好，我是家长助手，有什么可以帮你的吗？",
#     "chatTemplate": "你好，我是家长助手，有什么可以帮你的吗？"
#   }
# }