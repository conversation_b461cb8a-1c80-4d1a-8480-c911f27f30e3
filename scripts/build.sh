#!/bin/bash

# 确保脚本在出错时退出
set -e

# 设置变量
APP_NAME="aigc_server"
SDK_PATH="$(pwd)/sdk/VolcEngineRTC_Linux_3.60.1.29857238_x86_64_Release"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 设置 Go 环境变量
export CGO_ENABLED=1
export GOOS=linux
export GOARCH=amd64
export HEADER_FILE_PATH="${SDK_PATH}/include"
export CGO_CXXFLAGS="-std=c++14 -I${HEADER_FILE_PATH}"
export CGO_LDFLAGS="-L$(pwd)/lib -latomic -lbytertc_ffmpeg_audio_extension -lbytenn -lbytertc_vp8codec_extension -lbytertc_nico_extension -lRTCFFmpeg -lbytertc_fdk-aac_extension -lVolcEngineRTC -lVolcEngineRTCWrapper '-Wl,-rpath=\$ORIGIN/lib'"

echo "Header file path: ${HEADER_FILE_PATH}"
echo "CGO_CXXFLAGS: ${CGO_CXXFLAGS}"
echo "CGO_LDFLAGS: ${CGO_LDFLAGS}"

# 编译
echo "编译 ${APP_NAME}..."
go build $(pwd)
# -ldflags "-X main.Version=${VERSION} -X main.BuildTime='${BUILD_TIME}' -X main.GitCommit=${COMMIT_HASH}"

echo "构建完成: ${APP_NAME}"
